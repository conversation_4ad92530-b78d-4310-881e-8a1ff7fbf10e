# SpriteGen Enhancement Guidelines & Technical Specifications

## Executive Summary

This document outlines comprehensive enhancement guidelines for transforming SpriteGen into a professional-grade AI sprite generation tool. The enhancements include multi-model AI integration with NANO BANANA (cloud) and Animagine XL 4.0 (local), 8-directional sprite support, advanced pixel art workflows, and production-ready export capabilities.

## 1. Multi-Model AI Integration

### 1.1 Unified AI Interface Architecture

```typescript
interface AIModel {
  name: string;
  type: 'cloud' | 'local';
  capabilities: ModelCapabilities;
  rateLimits?: RateLimitConfig;
}

interface ModelCapabilities {
  maxResolution: { width: number; height: number };
  supportedFormats: string[];
  directionalSupport: boolean;
  pixelArtOptimized: boolean;
  animationFrames: number;
}

interface UnifiedAIService {
  generateSprite(options: EnhancedGenerationOptions): Promise<GenerationResult>;
  selectOptimalModel(requirements: GenerationRequirements): AIModel;
  executeWithFallback(primary: AIModel, fallback: AIModel, options: any): Promise<any>;
}
```

### 1.2 Model Configuration

```typescript
const AI_MODELS: Record<string, AIModel> = {
  NANO_BANANA: {
    name: 'NANO BANANA (Gemini 2.5 Flash Image)',
    type: 'cloud',
    capabilities: {
      maxResolution: { width: 1024, height: 1024 },
      supportedFormats: ['PNG', 'JPG', 'WebP'],
      directionalSupport: true,
      pixelArtOptimized: false,
      animationFrames: 8
    },
    rateLimits: {
      maxRequestsPerMinute: 10,
      maxRequestsPerDay: 500,
      maxTokensPerMinute: 250000
    }
  },
  ANIMAGINE_XL: {
    name: 'Animagine XL 4.0 (Local)',
    type: 'local',
    capabilities: {
      maxResolution: { width: 1024, height: 1024 },
      supportedFormats: ['PNG', 'JPG'],
      directionalSupport: true,
      pixelArtOptimized: true,
      animationFrames: 12,
      animeStyleOptimized: true,
      characterFocused: true
    }
  }
};
```

### 1.3 Fallback Logic Implementation

```typescript
class EnhancedAIService {
  async generateWithFallback(options: GenerationOptions): Promise<GenerationResult> {
    const primaryModel = this.selectOptimalModel(options);
    const fallbackModel = this.selectFallbackModel(primaryModel);
    
    try {
      return await this.generateWithModel(primaryModel, options);
    } catch (error) {
      console.log(`Primary model ${primaryModel.name} failed, trying fallback...`);
      return await this.generateWithModel(fallbackModel, options);
    }
  }
}
```

## 2. Enhanced Sprite Generation Features

### 2.1 8-Directional Support

```typescript
enum Direction {
  NORTH = 'north',
  NORTHEAST = 'northeast', 
  EAST = 'east',
  SOUTHEAST = 'southeast',
  SOUTH = 'south',
  SOUTHWEST = 'southwest',
  WEST = 'west',
  NORTHWEST = 'northwest'
}

interface DirectionalGenerationOptions {
  directions: Direction[];
  autoRepose: boolean;
  isometricOptimization: boolean;
  perspectiveAngle?: number;
}

interface DirectionalSprite {
  direction: Direction;
  frames: string[]; // Base64 encoded frames
  metadata: {
    frameCount: number;
    duration: number;
    loopType: 'perfect' | 'standard';
  };
}
```

### 2.2 Animation Version System

```typescript
enum AnimationVersion {
  V2 = 'v2', // Idle-start consistency
  V3 = 'v3'  // Perfect loop with physics
}

interface AnimationVersionConfig {
  version: AnimationVersion;
  startWithIdle: boolean;
  endWithIdle: boolean;
  weaponConsistency: boolean;
  physicsEffects: boolean;
  clothSimulation?: boolean;
  fireEffects?: boolean;
}

const VERSION_CONFIGS: Record<AnimationVersion, AnimationVersionConfig> = {
  [AnimationVersion.V2]: {
    version: AnimationVersion.V2,
    startWithIdle: true,
    endWithIdle: false,
    weaponConsistency: true,
    physicsEffects: false
  },
  [AnimationVersion.V3]: {
    version: AnimationVersion.V3,
    startWithIdle: true,
    endWithIdle: true,
    weaponConsistency: true,
    physicsEffects: true,
    clothSimulation: true,
    fireEffects: true
  }
};
```

### 2.3 Enhanced Action Types

```typescript
enum ActionType {
  // Basic Actions
  IDLE = 'idle',
  WALK = 'walk',
  RUN = 'run',
  
  // Combat Actions
  ATTACK_MELEE = 'attack_melee',
  ATTACK_RANGED = 'attack_ranged',
  DEFEND = 'defend',
  BLOCK = 'block',
  DODGE = 'dodge',
  
  // Magic Actions
  CAST_SPELL = 'cast_spell',
  CHANNEL = 'channel',
  SUMMON = 'summon',
  
  // Movement Actions
  JUMP = 'jump',
  CLIMB = 'climb',
  CROUCH = 'crouch',
  SLIDE = 'slide',
  
  // Interaction Actions
  PICKUP = 'pickup',
  USE_ITEM = 'use_item',
  OPEN_DOOR = 'open_door',
  
  // Emotional Actions
  CELEBRATE = 'celebrate',
  HURT = 'hurt',
  DIE = 'die',
  TAUNT = 'taunt',
  
  // Custom Actions
  CUSTOM = 'custom'
}
```

## 3. File Upload System Implementation

### 3.1 Enhanced File Upload Service

```typescript
interface FileUploadConfig {
  maxFileSize: number; // 50MB
  supportedFormats: string[];
  uploadPath: string;
  processingOptions: ImageProcessingOptions;
}

interface ImageProcessingOptions {
  autoResize: boolean;
  maxDimensions: { width: number; height: number };
  formatConversion: boolean;
  qualityOptimization: boolean;
  pixelArtDetection: boolean;
}

class EnhancedFileUploadService {
  async uploadReferenceImage(file: Express.Multer.File): Promise<UploadResult> {
    // Validate file size and format
    await this.validateFile(file);
    
    // Process and optimize image
    const processedImage = await this.processImage(file);
    
    // Detect if pixel art
    const isPixelArt = await this.detectPixelArt(processedImage);
    
    // Store with metadata
    return await this.storeWithMetadata(processedImage, isPixelArt);
  }
  
  private async detectPixelArt(image: Buffer): Promise<boolean> {
    // Implement pixel art detection algorithm
    // Check for low color count, sharp edges, pixel patterns
  }
}
```

## 4. Pixel Art Workflow Enhancement

### 4.1 3-Step Pixel Art Pipeline

```typescript
interface PixelArtPipeline {
  upscale(input: Buffer, factor: number): Promise<Buffer>;
  generateWithAI(upscaled: Buffer, options: GenerationOptions): Promise<Buffer[]>;
  convertToPixelArt(frames: Buffer[], options: PixelArtOptions): Promise<Buffer[]>;
}

interface PixelArtOptions {
  pixelSize: number; // 1x, 2x, 4x, 8x
  colorPalette: ColorPalette;
  ditheringType: DitheringType;
  edgeSmoothing: boolean;
  colorReduction: number;
}

enum DitheringType {
  NONE = 'none',
  FLOYD_STEINBERG = 'floyd_steinberg',
  ORDERED = 'ordered',
  RANDOM = 'random'
}

class PixelArtProcessor {
  async processPixelArtWorkflow(
    input: Buffer,
    options: PixelArtOptions,
    generationOptions: GenerationOptions
  ): Promise<PixelArtResult> {
    // Step 1: Upscale
    const upscaled = await this.upscaleImage(input, 4);
    
    // Step 2: AI Generation
    const aiFrames = await this.generateWithAI(upscaled, generationOptions);
    
    // Step 3: Convert back to pixel art
    const pixelFrames = await this.convertToPixelArt(aiFrames, options);
    
    return {
      frames: pixelFrames,
      metadata: this.generateMetadata(options),
      originalSize: this.getImageDimensions(input),
      finalSize: this.getImageDimensions(pixelFrames[0])
    };
  }
}
```

### 4.2 Color Palette Management

```typescript
interface ColorPalette {
  id: string;
  name: string;
  colors: string[]; // Hex color codes
  type: PaletteType;
  isCustom: boolean;
}

enum PaletteType {
  RETRO_8BIT = 'retro_8bit',
  RETRO_16BIT = 'retro_16bit',
  GAMEBOY = 'gameboy',
  NES = 'nes',
  COMMODORE64 = 'commodore64',
  CUSTOM = 'custom'
}

const PREDEFINED_PALETTES: Record<PaletteType, ColorPalette> = {
  [PaletteType.GAMEBOY]: {
    id: 'gameboy',
    name: 'Game Boy',
    colors: ['#0F380F', '#306230', '#8BAC0F', '#9BBD0F'],
    type: PaletteType.GAMEBOY,
    isCustom: false
  }
  // ... other predefined palettes
};
```

## 5. API Specification

### 5.1 New API Endpoints

```typescript
// Multi-directional sprite generation
POST /api/generation/sprite/directional
{
  "referenceImageId": "uuid",
  "directions": ["north", "south", "east", "west"],
  "action": "walk",
  "animationVersion": "v3",
  "aiModel": "NANO_BANANA",
  "pixelArtOptions": {
    "pixelSize": 2,
    "colorPalette": "gameboy",
    "ditheringType": "floyd_steinberg"
  }
}

// File upload endpoint
POST /api/upload/reference-image
Content-Type: multipart/form-data
{
  "file": File, // Up to 50MB
  "processingOptions": {
    "autoResize": true,
    "detectPixelArt": true
  }
}

// Model selection and capabilities
GET /api/models/available
Response: {
  "models": [
    {
      "id": "NANO_BANANA",
      "name": "NANO BANANA (Cloud)",
      "status": "available",
      "capabilities": {...},
      "rateLimits": {...}
    },
    {
      "id": "ANIMAGINE_XL",
      "name": "Animagine XL 4.0 (Local)",
      "status": "available",
      "capabilities": {...}
    }
  ]
}

// Spritesheet export
POST /api/export/spritesheet
{
  "spriteGenerationId": "uuid",
  "format": "unity", // unity, godot, generic
  "includeMetadata": true,
  "frameArrangement": "grid" // grid, horizontal, vertical
}
```

### 5.2 Enhanced Generation Options

```typescript
interface EnhancedGenerationOptions {
  // Basic options (existing)
  prompt: string;
  pose: string;
  style: string;
  colorPalette?: any;

  // New enhanced options
  referenceImageId?: string;
  directions: Direction[];
  animationVersion: AnimationVersion;
  aiModel: string;

  // Pixel art specific
  pixelArtWorkflow?: boolean;
  pixelArtOptions?: PixelArtOptions;

  // Advanced features
  physicsEffects?: string[];
  customActionDefinition?: ActionDefinition;
  partialRegeneration?: {
    frameIndices: number[];
    preserveOthers: boolean;
  };
}
```

## 6. Local Model Integration Guide

### 6.1 Animagine XL 4.0 Setup Requirements

**Hardware Requirements:**
- **GPU**: NVIDIA RTX 4060 or better (12GB+ VRAM recommended)
- **RAM**: 32GB+ system memory (for optimal performance)
- **Storage**: 15GB+ free space for model files
- **CPU**: Modern multi-core processor (Intel i7/AMD Ryzen 7+)

**Software Dependencies:**
```bash
# Python environment setup
python -m venv animagine-xl-env
source animagine-xl-env/bin/activate  # Linux/Mac
# animagine-xl-env\Scripts\activate   # Windows

# Install dependencies
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
pip install diffusers transformers accelerate safetensors
pip install pillow opencv-python numpy
pip install huggingface-hub
```

**Model Download and Setup:**
```bash
# Download Animagine XL 4.0 model (requires Hugging Face account)
huggingface-cli login  # Login with your HF token

# Download the model to local cache
python -c "
from diffusers import StableDiffusionXLPipeline
import torch

# This will download the model to the cache
pipeline = StableDiffusionXLPipeline.from_pretrained(
    'cagliostrolab/animagine-xl-4.0',
    torch_dtype=torch.float16,
    use_safetensors=True,
    variant='fp16'
)
print('Model downloaded successfully!')
"
```

**Performance Optimization:**
```bash
# For systems with limited VRAM (8-12GB)
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Enable memory efficient attention
export XFORMERS_FORCE_DISABLE_TRITON=1
pip install xformers
```

### 6.2 Model Integration Architecture

```typescript
class AnimagineXLService {
  private pythonProcess: ChildProcess;
  private modelLoaded: boolean = false;

  async initializeModel(): Promise<void> {
    // Start Python process with Animagine XL 4.0 model
    this.pythonProcess = spawn('python', [
      'python-bridge/animagine_interface.py'
    ], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: process.cwd(),
      env: {
        ...process.env,
        CUDA_VISIBLE_DEVICES: '0',
        HF_HOME: './ai-models/cache'
      }
    });

    // Wait for model to load
    await this.waitForModelReady();
    this.modelLoaded = true;
  }

  async generateSprite(options: GenerationOptions): Promise<GenerationResult> {
    if (!this.modelLoaded) {
      await this.initializeModel();
    }

    // Send generation request to Python process
    const request = JSON.stringify(options);
    this.pythonProcess.stdin?.write(request + '\n');

    // Read response
    return await this.readPythonResponse();
  }
}
```

### 6.3 Directory Structure for Local Model

```bash
# Animagine XL 4.0 Local Setup
spritegen/
├── ai-models/
│   ├── animagine-xl-4.0/
│   │   ├── model_index.json
│   │   ├── scheduler/
│   │   ├── text_encoder/
│   │   ├── tokenizer/
│   │   ├── unet/
│   │   ├── vae/
│   │   └── requirements.txt
│   ├── cache/  # Hugging Face cache directory
│   ├── setup.sh
│   └── model-manager.ts
├── python-bridge/
│   ├── animagine_interface.py
│   ├── pixel_art_processor.py
│   └── direction_generator.py
```

### 6.4 Python Bridge Implementation for Animagine XL 4.0

```python
# python-bridge/animagine_interface.py
import json
import sys
import torch
from diffusers import StableDiffusionXLPipeline
from PIL import Image
import base64
import io
import time

class AnimagineXLInterface:
    def __init__(self):
        self.pipeline = None
        self.load_model()

    def load_model(self):
        """Load the Animagine XL 4.0 model from Hugging Face"""
        model_id = "cagliostrolab/animagine-xl-4.0"

        self.pipeline = StableDiffusionXLPipeline.from_pretrained(
            model_id,
            torch_dtype=torch.float16,
            use_safetensors=True,
            variant="fp16"
        )

        # Enable memory efficient attention
        self.pipeline.enable_attention_slicing()
        self.pipeline.enable_vae_slicing()

        # Move to GPU
        self.pipeline.to("cuda")

        print("ANIMAGINE_XL_READY", flush=True)

    def build_prompt(self, options):
        """Build optimized prompt for Animagine XL 4.0"""
        base_prompt = options.get('prompt', '')
        pose = options.get('pose', 'standing')
        style = options.get('style', 'anime')

        # Animagine XL specific prompt optimization
        prompt = f"{base_prompt}, {pose}, {style}, "
        prompt += "masterpiece, best quality, very aesthetic, absurdres, "
        prompt += "1girl, solo, full body, simple background, "
        prompt += "game character, sprite art, clean lines, "
        prompt += "detailed character design, anime style"

        # Add directional context if specified
        direction = options.get('direction')
        if direction:
            direction_map = {
                'north': 'facing away, back view',
                'south': 'facing forward, front view',
                'east': 'facing right, side view',
                'west': 'facing left, side view',
                'northeast': 'three-quarter back right view',
                'northwest': 'three-quarter back left view',
                'southeast': 'three-quarter front right view',
                'southwest': 'three-quarter front left view'
            }
            prompt += f", {direction_map.get(direction, 'front view')}"

        return prompt

    def generate_sprite(self, options):
        """Generate sprite using Animagine XL 4.0"""
        prompt = self.build_prompt(options)
        negative_prompt = "lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry, multiple views, reference sheet"

        # Generate image
        with torch.autocast("cuda"):
            image = self.pipeline(
                prompt=prompt,
                negative_prompt=negative_prompt,
                num_inference_steps=28,
                guidance_scale=12.0,
                width=options.get('width', 1024),
                height=options.get('height', 1024)
            ).images[0]

        # Resize to sprite dimensions if needed
        target_size = options.get('target_size', 512)
        if image.size[0] != target_size or image.size[1] != target_size:
            image = image.resize((target_size, target_size), Image.LANCZOS)

        # Convert to base64
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        image_base64 = base64.b64encode(buffer.getvalue()).decode()

        return {
            "success": True,
            "imageData": f"data:image/png;base64,{image_base64}",
            "metadata": {
                "model": "animagine_xl_4.0",
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "timestamp": time.time(),
                "resolution": f"{image.size[0]}x{image.size[1]}"
            }
        }

if __name__ == "__main__":
    interface = AnimagineXLInterface()

    # Listen for requests
    for line in sys.stdin:
        try:
            options = json.loads(line.strip())
            result = interface.generate_sprite(options)
            print(json.dumps(result), flush=True)
        except Exception as e:
            error_result = {
                "success": False,
                "error": str(e),
                "model": "animagine_xl_4.0"
            }
            print(json.dumps(error_result), flush=True)
```

## 7. User Interface Requirements

### 7.1 Model Selection Interface

```tsx
interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (model: string) => void;
  modelStatus: Record<string, ModelStatus>;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onModelChange,
  modelStatus
}) => {
  return (
    <div className="model-selector">
      <h3>AI Model Selection</h3>
      <div className="model-options">
        <div className={`model-option ${selectedModel === 'NANO_BANANA' ? 'selected' : ''}`}>
          <input
            type="radio"
            id="nano-banana"
            name="aiModel"
            value="NANO_BANANA"
            checked={selectedModel === 'NANO_BANANA'}
            onChange={(e) => onModelChange(e.target.value)}
          />
          <label htmlFor="nano-banana">
            <div className="model-info">
              <h4>NANO BANANA (Cloud)</h4>
              <p>Google Gemini 2.5 Flash Image</p>
              <div className="model-status">
                <span className={`status-indicator ${modelStatus.NANO_BANANA?.status}`}>
                  {modelStatus.NANO_BANANA?.status}
                </span>
                <span className="rate-limit">
                  {modelStatus.NANO_BANANA?.rateLimits?.current}/
                  {modelStatus.NANO_BANANA?.rateLimits?.limit} requests
                </span>
              </div>
            </div>
          </label>
        </div>

        <div className={`model-option ${selectedModel === 'ANIMAGINE_XL' ? 'selected' : ''}`}>
          <input
            type="radio"
            id="animagine-xl"
            name="aiModel"
            value="ANIMAGINE_XL"
            checked={selectedModel === 'ANIMAGINE_XL'}
            onChange={(e) => onModelChange(e.target.value)}
          />
          <label htmlFor="animagine-xl">
            <div className="model-info">
              <h4>Animagine XL 4.0 (Local)</h4>
              <p>Anime-style character generation optimized</p>
              <div className="model-status">
                <span className={`status-indicator ${modelStatus.ANIMAGINE_XL?.status}`}>
                  {modelStatus.ANIMAGINE_XL?.status}
                </span>
                <span className="performance">
                  No rate limits • Local processing • High quality
                </span>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
  );
};
```

### 7.2 8-Directional Compass Interface

```tsx
interface DirectionSelectorProps {
  selectedDirections: Direction[];
  onDirectionToggle: (direction: Direction) => void;
  isometricMode: boolean;
}

const DirectionSelector: React.FC<DirectionSelectorProps> = ({
  selectedDirections,
  onDirectionToggle,
  isometricMode
}) => {
  const directions = [
    { dir: Direction.NORTH, label: 'N', position: { top: '10%', left: '50%' } },
    { dir: Direction.NORTHEAST, label: 'NE', position: { top: '20%', left: '75%' } },
    { dir: Direction.EAST, label: 'E', position: { top: '50%', left: '90%' } },
    { dir: Direction.SOUTHEAST, label: 'SE', position: { top: '80%', left: '75%' } },
    { dir: Direction.SOUTH, label: 'S', position: { top: '90%', left: '50%' } },
    { dir: Direction.SOUTHWEST, label: 'SW', position: { top: '80%', left: '25%' } },
    { dir: Direction.WEST, label: 'W', position: { top: '50%', left: '10%' } },
    { dir: Direction.NORTHWEST, label: 'NW', position: { top: '20%', left: '25%' } }
  ];

  return (
    <div className="direction-selector">
      <h3>Direction Selection</h3>
      <div className="compass-container">
        <div className="compass">
          {directions.map(({ dir, label, position }) => (
            <button
              key={dir}
              className={`direction-button ${selectedDirections.includes(dir) ? 'selected' : ''}`}
              style={position}
              onClick={() => onDirectionToggle(dir)}
              title={`Generate ${dir} facing sprite`}
            >
              {label}
            </button>
          ))}
          <div className="compass-center">
            <div className="character-icon">👤</div>
          </div>
        </div>
        <div className="direction-controls">
          <button onClick={() => selectAllDirections()}>Select All</button>
          <button onClick={() => selectCardinalDirections()}>Cardinal Only</button>
          <button onClick={() => clearDirections()}>Clear All</button>
        </div>
      </div>
    </div>
  );
};
```

### 7.3 Advanced Options Panel

```tsx
interface AdvancedOptionsProps {
  options: AdvancedGenerationOptions;
  onOptionsChange: (options: Partial<AdvancedGenerationOptions>) => void;
  isPixelArt: boolean;
}

const AdvancedOptionsPanel: React.FC<AdvancedOptionsProps> = ({
  options,
  onOptionsChange,
  isPixelArt
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="advanced-options">
      <button
        className="toggle-button"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        Advanced Options {isExpanded ? '▼' : '▶'}
      </button>

      {isExpanded && (
        <div className="options-content">
          {/* Animation Version Selector */}
          <div className="option-group">
            <h4>Animation Version</h4>
            <div className="version-selector">
              <label>
                <input
                  type="radio"
                  name="animationVersion"
                  value="v2"
                  checked={options.animationVersion === 'v2'}
                  onChange={(e) => onOptionsChange({ animationVersion: e.target.value as AnimationVersion })}
                />
                V2 - Idle Start Consistency
              </label>
              <label>
                <input
                  type="radio"
                  name="animationVersion"
                  value="v3"
                  checked={options.animationVersion === 'v3'}
                  onChange={(e) => onOptionsChange({ animationVersion: e.target.value as AnimationVersion })}
                />
                V3 - Perfect Loop + Physics
              </label>
            </div>
          </div>

          {/* Pixel Art Options */}
          {isPixelArt && (
            <div className="option-group">
              <h4>Pixel Art Settings</h4>
              <div className="pixel-options">
                <label>
                  Pixel Size:
                  <select
                    value={options.pixelArtOptions?.pixelSize || 2}
                    onChange={(e) => onOptionsChange({
                      pixelArtOptions: {
                        ...options.pixelArtOptions,
                        pixelSize: parseInt(e.target.value)
                      }
                    })}
                  >
                    <option value={1}>1x</option>
                    <option value={2}>2x</option>
                    <option value={4}>4x</option>
                    <option value={8}>8x</option>
                  </select>
                </label>

                <label>
                  Color Palette:
                  <select
                    value={options.pixelArtOptions?.colorPalette || 'retro_8bit'}
                    onChange={(e) => onOptionsChange({
                      pixelArtOptions: {
                        ...options.pixelArtOptions,
                        colorPalette: e.target.value
                      }
                    })}
                  >
                    <option value="retro_8bit">Retro 8-bit</option>
                    <option value="gameboy">Game Boy</option>
                    <option value="nes">NES</option>
                    <option value="custom">Custom</option>
                  </select>
                </label>

                <label>
                  Dithering:
                  <select
                    value={options.pixelArtOptions?.ditheringType || 'none'}
                    onChange={(e) => onOptionsChange({
                      pixelArtOptions: {
                        ...options.pixelArtOptions,
                        ditheringType: e.target.value as DitheringType
                      }
                    })}
                  >
                    <option value="none">None</option>
                    <option value="floyd_steinberg">Floyd-Steinberg</option>
                    <option value="ordered">Ordered</option>
                    <option value="random">Random</option>
                  </select>
                </label>
              </div>
            </div>
          )}

          {/* Physics Effects (V3 only) */}
          {options.animationVersion === 'v3' && (
            <div className="option-group">
              <h4>Physics Effects</h4>
              <div className="physics-options">
                <label>
                  <input
                    type="checkbox"
                    checked={options.physicsEffects?.includes('cloth') || false}
                    onChange={(e) => {
                      const effects = options.physicsEffects || [];
                      if (e.target.checked) {
                        onOptionsChange({ physicsEffects: [...effects, 'cloth'] });
                      } else {
                        onOptionsChange({ physicsEffects: effects.filter(e => e !== 'cloth') });
                      }
                    }}
                  />
                  Cloth Physics
                </label>
                <label>
                  <input
                    type="checkbox"
                    checked={options.physicsEffects?.includes('fire') || false}
                    onChange={(e) => {
                      const effects = options.physicsEffects || [];
                      if (e.target.checked) {
                        onOptionsChange({ physicsEffects: [...effects, 'fire'] });
                      } else {
                        onOptionsChange({ physicsEffects: effects.filter(e => e !== 'fire') });
                      }
                    }}
                  />
                  Fire Effects
                </label>
              </div>
            </div>
          )}

          {/* Export Options */}
          <div className="option-group">
            <h4>Export Settings</h4>
            <div className="export-options">
              <label>
                Target Engine:
                <select
                  value={options.exportFormat || 'generic'}
                  onChange={(e) => onOptionsChange({ exportFormat: e.target.value })}
                >
                  <option value="generic">Generic Spritesheet</option>
                  <option value="unity">Unity</option>
                  <option value="godot">Godot</option>
                  <option value="unreal">Unreal Engine</option>
                </select>
              </label>

              <label>
                Frame Rate:
                <input
                  type="number"
                  min="1"
                  max="60"
                  value={options.frameRate || 12}
                  onChange={(e) => onOptionsChange({ frameRate: parseInt(e.target.value) })}
                />
                FPS
              </label>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
```

## 8. Database Schema Changes

### 8.1 Enhanced Database Schema

```sql
-- Enhanced reference images table
ALTER TABLE reference_images ADD COLUMN IF NOT EXISTS
  color_palette JSONB,
  dominant_colors JSONB,
  style_analysis JSONB,
  processing_metadata JSONB;

-- New tables for enhanced features
CREATE TABLE sprite_generations_enhanced (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  reference_image_id UUID REFERENCES reference_images(id),
  user_id VARCHAR(255),

  -- Generation parameters
  generation_options JSONB NOT NULL,
  ai_model_used VARCHAR(50) NOT NULL,
  animation_version VARCHAR(10) NOT NULL,

  -- Directional data
  directions_generated JSONB NOT NULL, -- Array of Direction enums
  directional_sprites JSONB NOT NULL,  -- Map of direction -> sprite data

  -- Metadata
  generation_timestamp TIMESTAMP DEFAULT NOW(),
  processing_time_ms INTEGER,
  total_frames INTEGER,

  -- Status tracking
  status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed
  error_message TEXT,

  -- Export data
  spritesheet_data JSONB, -- Generated spritesheet information
  export_metadata JSONB   -- Game engine specific metadata
);

CREATE TABLE custom_actions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id VARCHAR(255) NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  action_definition JSONB NOT NULL,
  training_data JSONB, -- For custom action training
  created_at TIMESTAMP DEFAULT NOW(),
  is_public BOOLEAN DEFAULT FALSE
);

CREATE TABLE color_palettes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id VARCHAR(255),
  name VARCHAR(100) NOT NULL,
  colors JSONB NOT NULL, -- Array of hex color codes
  palette_type VARCHAR(50) NOT NULL,
  is_predefined BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_sprite_generations_user_id ON sprite_generations_enhanced(user_id);
CREATE INDEX idx_sprite_generations_timestamp ON sprite_generations_enhanced(generation_timestamp);
CREATE INDEX idx_sprite_generations_status ON sprite_generations_enhanced(status);
CREATE INDEX idx_reference_images_user_id ON reference_images(user_id);
CREATE INDEX idx_custom_actions_user_id ON custom_actions(user_id);
```

## 9. Implementation Timeline

### 9.1 Phase 1: Foundation (Weeks 1-3)
**Priority: HIGH**

- [ ] **Week 1**: Multi-model architecture setup
  - Implement unified AI interface
  - Create model selection system
  - Set up fallback logic
  - Update rate limiting for multiple models

- [ ] **Week 2**: File upload system
  - Implement 50MB file upload support
  - Add format validation and processing
  - Create pixel art detection algorithm
  - Set up file storage and metadata system

- [ ] **Week 3**: Database schema updates
  - Implement enhanced database schema
  - Create migration scripts
  - Set up new API endpoints structure
  - Update existing endpoints for backward compatibility

### 9.2 Phase 2: Core Features (Weeks 4-7)
**Priority: HIGH**

- [ ] **Week 4**: 8-directional sprite generation
  - Implement direction enumeration and logic
  - Create directional generation algorithms
  - Build compass UI component
  - Test directional consistency

- [ ] **Week 5**: Animation version system
  - Implement V2 and V3 animation logic
  - Add idle-start and perfect-loop functionality
  - Create weapon consistency algorithms
  - Build version selector UI

- [ ] **Week 6**: Local model integration (Animagine XL 4.0)
  - Set up Python bridge architecture for Animagine XL 4.0
  - Implement Hugging Face model interface
  - Create model installation and caching scripts
  - Test local generation pipeline with anime-style optimization

- [ ] **Week 7**: Enhanced action types
  - Expand action type enumeration
  - Implement action-specific generation logic
  - Create action selector UI
  - Add custom action support

### 9.3 Phase 3: Advanced Features (Weeks 8-11)
**Priority: MEDIUM**

- [ ] **Week 8**: Pixel art workflow
  - Implement 3-step pixel art pipeline
  - Create upscaling and conversion algorithms
  - Build pixel art options UI
  - Add color palette management

- [ ] **Week 9**: Physics effects (V3)
  - Implement cloth physics simulation
  - Add fire effects generation
  - Create physics options UI
  - Test effect consistency across frames

- [ ] **Week 10**: Spritesheet export system
  - Implement spritesheet generation
  - Add game engine metadata support
  - Create export options UI
  - Test with Unity, Godot, Unreal

- [ ] **Week 11**: Advanced UI components
  - Build comprehensive options panel
  - Implement drag-and-drop upload
  - Create progress tracking UI
  - Add batch operation support

### 9.4 Phase 4: Polish & Optimization (Weeks 12-14)
**Priority: MEDIUM**

- [ ] **Week 12**: Performance optimization
  - Optimize AI model switching
  - Implement caching strategies
  - Reduce memory usage for large files
  - Optimize database queries

- [ ] **Week 13**: Testing and quality assurance
  - Comprehensive testing of all features
  - Performance testing with large files
  - Cross-browser compatibility testing
  - Load testing for concurrent users

- [ ] **Week 14**: Documentation and deployment
  - Update API documentation
  - Create user guides for new features
  - Prepare deployment scripts
  - Final integration testing

### 9.5 Phase 5: Future Enhancements (Weeks 15+)
**Priority: LOW**

- [ ] **Custom action training**: Machine learning pipeline for user-defined actions
- [ ] **Collaborative features**: Sharing and community sprite libraries
- [ ] **Advanced physics**: More sophisticated physics simulations
- [ ] **3D sprite generation**: Isometric and pseudo-3D sprite support
- [ ] **Animation timeline editor**: Frame-by-frame editing capabilities

## 10. Testing Strategy

### 10.1 Unit Testing

```typescript
// Model selection testing
describe('UnifiedAIService', () => {
  test('should select optimal model based on requirements', () => {
    const service = new UnifiedAIService();
    const requirements = {
      pixelArt: true,
      directions: [Direction.NORTH, Direction.SOUTH],
      animationVersion: AnimationVersion.V3
    };

    const selectedModel = service.selectOptimalModel(requirements);
    expect(selectedModel.name).toBe('ANIMAGINE_XL');
  });

  test('should fallback to secondary model on failure', async () => {
    const service = new UnifiedAIService();
    const options = { /* test options */ };

    // Mock primary model failure
    jest.spyOn(service, 'generateWithModel')
      .mockRejectedValueOnce(new Error('Model unavailable'))
      .mockResolvedValueOnce({ success: true });

    const result = await service.executeWithFallback(
      AI_MODELS.NANO_BANANA,
      AI_MODELS.ANIMAGINE_XL,
      options
    );

    expect(result.success).toBe(true);
  });
});

// Directional generation testing
describe('DirectionalSpriteGenerator', () => {
  test('should generate sprites for all 8 directions', async () => {
    const generator = new DirectionalSpriteGenerator();
    const options = {
      directions: Object.values(Direction),
      action: ActionType.WALK,
      animationVersion: AnimationVersion.V2
    };

    const result = await generator.generateDirectionalSprites(options);

    expect(result.directionalSprites).toHaveLength(8);
    expect(result.directionalSprites[0].direction).toBe(Direction.NORTH);
  });
});

// Pixel art pipeline testing
describe('PixelArtProcessor', () => {
  test('should process 3-step pixel art workflow', async () => {
    const processor = new PixelArtProcessor();
    const inputBuffer = await fs.readFile('test-assets/character.png');
    const options = {
      pixelSize: 2,
      colorPalette: PREDEFINED_PALETTES.GAMEBOY,
      ditheringType: DitheringType.FLOYD_STEINBERG
    };

    const result = await processor.processPixelArtWorkflow(
      inputBuffer,
      options,
      { /* generation options */ }
    );

    expect(result.frames).toHaveLength(8);
    expect(result.metadata.pixelSize).toBe(2);
  });
});
```

### 10.2 Integration Testing

```typescript
// End-to-end API testing
describe('Enhanced Sprite Generation API', () => {
  test('should handle complete directional generation workflow', async () => {
    // Upload reference image
    const uploadResponse = await request(app)
      .post('/api/upload/reference-image')
      .attach('file', 'test-assets/character.png')
      .expect(200);

    const referenceImageId = uploadResponse.body.id;

    // Generate directional sprites
    const generationResponse = await request(app)
      .post('/api/generation/sprite/directional')
      .send({
        referenceImageId,
        directions: ['north', 'south', 'east', 'west'],
        action: 'walk',
        animationVersion: 'v3',
        aiModel: 'ANIMAGINE_XL'
      })
      .expect(200);

    expect(generationResponse.body.success).toBe(true);
    expect(generationResponse.body.directionalSprites).toHaveLength(4);

    // Export spritesheet
    const exportResponse = await request(app)
      .post('/api/export/spritesheet')
      .send({
        spriteGenerationId: generationResponse.body.id,
        format: 'unity',
        includeMetadata: true
      })
      .expect(200);

    expect(exportResponse.body.spritesheet).toBeDefined();
    expect(exportResponse.body.metadata.unity).toBeDefined();
  });
});
```

### 10.3 Performance Testing

```typescript
// Load testing for file uploads
describe('File Upload Performance', () => {
  test('should handle 50MB file uploads efficiently', async () => {
    const largeFile = await generateTestFile(50 * 1024 * 1024); // 50MB
    const startTime = Date.now();

    const response = await request(app)
      .post('/api/upload/reference-image')
      .attach('file', largeFile)
      .expect(200);

    const uploadTime = Date.now() - startTime;
    expect(uploadTime).toBeLessThan(30000); // Should complete within 30 seconds
    expect(response.body.success).toBe(true);
  });

  test('should handle concurrent uploads', async () => {
    const uploadPromises = Array.from({ length: 5 }, () =>
      request(app)
        .post('/api/upload/reference-image')
        .attach('file', 'test-assets/character.png')
    );

    const results = await Promise.all(uploadPromises);
    results.forEach(result => {
      expect(result.status).toBe(200);
      expect(result.body.success).toBe(true);
    });
  });
});

// Memory usage testing
describe('Memory Management', () => {
  test('should not leak memory during batch generation', async () => {
    const initialMemory = process.memoryUsage().heapUsed;

    // Generate multiple sprite batches
    for (let i = 0; i < 10; i++) {
      await request(app)
        .post('/api/generation/sprite/directional')
        .send({
          directions: Object.values(Direction),
          action: 'walk',
          animationVersion: 'v2'
        });
    }

    // Force garbage collection
    if (global.gc) global.gc();

    const finalMemory = process.memoryUsage().heapUsed;
    const memoryIncrease = finalMemory - initialMemory;

    // Memory increase should be reasonable (less than 100MB)
    expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024);
  });
});
```

## 11. Security Considerations

### 11.1 File Upload Security

```typescript
// File validation and sanitization
class SecureFileUploadService {
  private readonly ALLOWED_MIME_TYPES = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp'
  ];

  private readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

  async validateFile(file: Express.Multer.File): Promise<void> {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error('File size exceeds 50MB limit');
    }

    // Check MIME type
    if (!this.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
      throw new Error('Unsupported file format');
    }

    // Validate file header (magic bytes)
    await this.validateFileHeader(file.buffer);

    // Scan for malicious content
    await this.scanForMalware(file.buffer);
  }

  private async validateFileHeader(buffer: Buffer): Promise<void> {
    const header = buffer.slice(0, 16);

    // Check for valid image headers
    const validHeaders = [
      [0xFF, 0xD8, 0xFF], // JPEG
      [0x89, 0x50, 0x4E, 0x47], // PNG
      [0x47, 0x49, 0x46], // GIF
      [0x52, 0x49, 0x46, 0x46] // WebP
    ];

    const isValid = validHeaders.some(validHeader =>
      validHeader.every((byte, index) => header[index] === byte)
    );

    if (!isValid) {
      throw new Error('Invalid file header');
    }
  }
}
```

### 11.2 API Security

```typescript
// Rate limiting and authentication
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';

// Enhanced rate limiting for file uploads
const uploadRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 uploads per windowMs
  message: 'Too many file uploads, please try again later',
  standardHeaders: true,
  legacyHeaders: false
});

// API key validation middleware
const validateApiKey = (req: Request, res: Response, next: NextFunction) => {
  const apiKey = req.headers['x-api-key'];

  if (!apiKey || !isValidApiKey(apiKey)) {
    return res.status(401).json({ error: 'Invalid or missing API key' });
  }

  next();
};

// Input sanitization
const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  // Sanitize all string inputs
  Object.keys(req.body).forEach(key => {
    if (typeof req.body[key] === 'string') {
      req.body[key] = req.body[key].trim().slice(0, 1000); // Limit length
    }
  });

  next();
};

// Apply security middleware
app.use(helmet());
app.use('/api/upload', uploadRateLimit);
app.use('/api', validateApiKey);
app.use('/api', sanitizeInput);
```

## 12. Monitoring and Analytics

### 12.1 Performance Monitoring

```typescript
// Performance tracking service
class PerformanceMonitor {
  async trackGeneration(
    modelUsed: string,
    generationType: string,
    startTime: number,
    endTime: number,
    success: boolean
  ): Promise<void> {
    const metrics = {
      model: modelUsed,
      type: generationType,
      duration: endTime - startTime,
      success,
      timestamp: new Date(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    };

    // Store metrics in database
    await this.storeMetrics(metrics);

    // Send to monitoring service (e.g., DataDog, New Relic)
    await this.sendToMonitoring(metrics);
  }

  async getPerformanceReport(timeRange: string): Promise<PerformanceReport> {
    return {
      averageGenerationTime: await this.getAverageGenerationTime(timeRange),
      successRate: await this.getSuccessRate(timeRange),
      modelUsageStats: await this.getModelUsageStats(timeRange),
      errorBreakdown: await this.getErrorBreakdown(timeRange)
    };
  }
}

// Usage tracking
class UsageAnalytics {
  async trackFeatureUsage(feature: string, userId?: string): Promise<void> {
    const usage = {
      feature,
      userId,
      timestamp: new Date(),
      sessionId: this.getSessionId(),
      userAgent: this.getUserAgent()
    };

    await this.storeUsage(usage);
  }

  async getUsageReport(): Promise<UsageReport> {
    return {
      mostUsedFeatures: await this.getMostUsedFeatures(),
      userEngagement: await this.getUserEngagement(),
      featureAdoption: await this.getFeatureAdoption(),
      modelPreferences: await this.getModelPreferences()
    };
  }
}
```

### 12.2 Error Tracking

```typescript
// Enhanced error tracking
class ErrorTracker {
  async trackError(
    error: Error,
    context: ErrorContext,
    userId?: string
  ): Promise<void> {
    const errorData = {
      message: error.message,
      stack: error.stack,
      context,
      userId,
      timestamp: new Date(),
      severity: this.determineSeverity(error),
      fingerprint: this.generateFingerprint(error)
    };

    // Store in database
    await this.storeError(errorData);

    // Send to error tracking service (e.g., Sentry)
    await this.sendToErrorService(errorData);

    // Alert if critical
    if (errorData.severity === 'critical') {
      await this.sendAlert(errorData);
    }
  }

  private determineSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    if (error.message.includes('ECONNREFUSED')) return 'critical';
    if (error.message.includes('Rate limit')) return 'medium';
    if (error.message.includes('Validation')) return 'low';
    return 'medium';
  }
}
```

## 13. Conclusion

This comprehensive enhancement specification provides a roadmap for transforming SpriteGen into a professional-grade AI sprite generation tool using NANO BANANA (cloud) and Animagine XL 4.0 (local) models. The proposed enhancements will enable:

### Key Benefits:
- **Multi-model flexibility** with NANO BANANA (cloud) and Animagine XL 4.0 (local) AI options
- **Professional game development features** including 8-directional sprites and animation versions
- **Advanced pixel art workflows** with customizable processing pipelines
- **Production-ready exports** with game engine compatibility
- **Anime-optimized generation** with Animagine XL 4.0's character-focused capabilities
- **Scalable architecture** supporting future enhancements

### Success Metrics:
- **User Engagement**: Increased session duration and feature adoption
- **Generation Quality**: Higher user satisfaction with sprite output
- **Performance**: Sub-30-second generation times for complex requests
- **Reliability**: 99.5% uptime with graceful error handling
- **Scalability**: Support for 100+ concurrent users

### Implementation Priority:
1. **Phase 1-2 (Weeks 1-7)**: Foundation and core features - **CRITICAL**
2. **Phase 3 (Weeks 8-11)**: Advanced features - **HIGH**
3. **Phase 4 (Weeks 12-14)**: Polish and optimization - **MEDIUM**
4. **Phase 5 (Weeks 15+)**: Future enhancements - **LOW**

The implementation timeline spans 14 weeks with clear priorities and deliverables, ensuring a systematic rollout of features while maintaining backward compatibility and system stability.

### Next Steps:
1. Review and approve technical specifications
2. Set up development environment for local model integration
3. Begin Phase 1 implementation with multi-model architecture
4. Establish testing framework and CI/CD pipeline
5. Create detailed project management timeline with milestones

This specification serves as the definitive guide for implementing advanced AI sprite generation capabilities in SpriteGen, positioning it as a leading tool for game developers and digital artists.
```
