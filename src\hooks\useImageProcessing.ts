import { useState, useCallback } from 'react';
import { Sprite, SpritesheetExport } from '../types';
import {
  uploadImage,
  removeBackground,
  resizeImage,
  generateSpritesheet,
  pixelateImage,
  getImageInfo,
  SpritesheetGenerationRequest
} from '../utils/apiClient';

export interface UseImageProcessingReturn {
  // State
  isProcessing: boolean;
  error: string | null;

  // Actions
  uploadAndProcessImage: (file: File) => Promise<string | null>;
  processRemoveBackground: (imageData: string, backgroundColor?: string, tolerance?: number) => Promise<string | null>;
  processResizeImage: (imageData: string, width: number, height: number, maintainAspectRatio?: boolean) => Promise<string | null>;
  processPixelateImage: (imageData: string, pixelSize?: number) => Promise<string | null>;
  processSpritesheet: (sprites: Sprite[], columns?: number, padding?: number, backgroundColor?: string) => Promise<SpritesheetExport | null>;
  getImageInformation: (imageData: string) => Promise<any>;

  // Utility
  clearError: () => void;
}

export function useImageProcessing(): UseImageProcessingReturn {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const uploadAndProcessImage = useCallback(async (file: File): Promise<string | null> => {
    setIsProcessing(true);
    setError(null);

    try {
      const response = await uploadImage(file);
      
      if (response.success && response.imageData) {
        return response.imageData;
      } else {
        throw new Error('Failed to upload image');
      }
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const processRemoveBackground = useCallback(async (
    imageData: string,
    backgroundColor: string = '#FF00FF',
    tolerance: number = 10
  ): Promise<string | null> => {
    setIsProcessing(true);
    setError(null);

    try {
      const response = await removeBackground(imageData, backgroundColor, tolerance);
      
      if (response.success && response.imageData) {
        return response.imageData;
      } else {
        throw new Error('Failed to remove background');
      }
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const processResizeImage = useCallback(async (
    imageData: string,
    width: number,
    height: number,
    maintainAspectRatio: boolean = true
  ): Promise<string | null> => {
    setIsProcessing(true);
    setError(null);

    try {
      const response = await resizeImage(imageData, width, height, maintainAspectRatio);
      
      if (response.success && response.imageData) {
        return response.imageData;
      } else {
        throw new Error('Failed to resize image');
      }
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const processPixelateImage = useCallback(async (
    imageData: string,
    pixelSize: number = 4
  ): Promise<string | null> => {
    setIsProcessing(true);
    setError(null);

    try {
      const response = await pixelateImage(imageData, pixelSize);
      
      if (response.success && response.imageData) {
        return response.imageData;
      } else {
        throw new Error('Failed to pixelate image');
      }
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const processSpritesheet = useCallback(async (
    sprites: Sprite[],
    columns: number = 4,
    padding: number = 2,
    backgroundColor: string = 'transparent'
  ): Promise<SpritesheetExport | null> => {
    setIsProcessing(true);
    setError(null);

    try {
      const request: SpritesheetGenerationRequest = {
        sprites,
        columns,
        padding,
        backgroundColor
      };

      const response = await generateSpritesheet(request);
      
      if (response.success && response.imageData && response.metadata) {
        return {
          imageData: response.imageData,
          metadata: response.metadata
        };
      } else {
        throw new Error('Failed to generate spritesheet');
      }
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const getImageInformation = useCallback(async (imageData: string) => {
    setIsProcessing(true);
    setError(null);

    try {
      const response = await getImageInfo(imageData);
      
      if (response.success && response.info) {
        return response.info;
      } else {
        throw new Error('Failed to get image information');
      }
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setIsProcessing(false);
    }
  }, []);

  return {
    // State
    isProcessing,
    error,

    // Actions
    uploadAndProcessImage,
    processRemoveBackground,
    processResizeImage,
    processPixelateImage,
    processSpritesheet,
    getImageInformation,

    // Utility
    clearError
  };
}
