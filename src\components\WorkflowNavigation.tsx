import React from 'react';
import { WorkflowStage } from '../types';

interface WorkflowNavigationProps {
  currentStage: WorkflowStage;
  onStageChange: (stage: WorkflowStage) => void;
  canNavigate: {
    conception: boolean;
    generation: boolean;
    export: boolean;
  };
}

const WorkflowNavigation: React.FC<WorkflowNavigationProps> = ({
  currentStage,
  onStageChange,
  canNavigate
}) => {
  const stages = [
    {
      id: 'conception' as WorkflowStage,
      name: 'Conception',
      description: 'Define your character',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      )
    },
    {
      id: 'generation' as WorkflowStage,
      name: 'Generation',
      description: 'Create sprite poses',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      )
    },
    {
      id: 'export' as WorkflowStage,
      name: 'Export',
      description: 'Download your sprites',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      )
    }
  ];

  const getStageStatus = (stageId: WorkflowStage) => {
    if (stageId === currentStage) {
      return 'current';
    }
    
    const stageIndex = stages.findIndex(s => s.id === stageId);
    const currentIndex = stages.findIndex(s => s.id === currentStage);
    
    if (stageIndex < currentIndex) {
      return 'completed';
    }
    
    return 'upcoming';
  };

  const getStageClasses = (stageId: WorkflowStage) => {
    const status = getStageStatus(stageId);
    const canClick = canNavigate[stageId];
    
    const baseClasses = "flex items-center px-6 py-4 text-sm font-medium transition-all duration-200";
    
    if (status === 'current') {
      return `${baseClasses} bg-blue-600 text-white border-blue-600`;
    }
    
    if (status === 'completed') {
      return `${baseClasses} ${canClick ? 'bg-green-600 text-white border-green-600 hover:bg-green-700 cursor-pointer' : 'bg-gray-600 text-gray-300 border-gray-600 cursor-not-allowed'}`;
    }
    
    // upcoming
    return `${baseClasses} ${canClick ? 'bg-gray-700 text-gray-300 border-gray-600 hover:bg-gray-600 cursor-pointer' : 'bg-gray-800 text-gray-500 border-gray-700 cursor-not-allowed'}`;
  };

  const handleStageClick = (stageId: WorkflowStage) => {
    if (canNavigate[stageId]) {
      onStageChange(stageId);
    }
  };

  return (
    <nav className="bg-gray-800 border-b border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex space-x-8">
          {stages.map((stage, index) => {
            const status = getStageStatus(stage.id);
            const canClick = canNavigate[stage.id];
            
            return (
              <div key={stage.id} className="flex items-center">
                {/* Stage Button */}
                <button
                  onClick={() => handleStageClick(stage.id)}
                  disabled={!canClick}
                  className={getStageClasses(stage.id)}
                >
                  <div className="flex items-center space-x-3">
                    {/* Icon */}
                    <div className={`flex-shrink-0 ${status === 'completed' ? 'text-white' : ''}`}>
                      {status === 'completed' ? (
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        stage.icon
                      )}
                    </div>
                    
                    {/* Text */}
                    <div className="text-left">
                      <div className="font-semibold">{stage.name}</div>
                      <div className="text-xs opacity-75">{stage.description}</div>
                    </div>
                  </div>
                </button>

                {/* Arrow */}
                {index < stages.length - 1 && (
                  <div className="flex-shrink-0 w-5 h-5 text-gray-500 mx-4">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </nav>
  );
};

export default WorkflowNavigation;
