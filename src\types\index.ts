// Core sprite and character types
export interface Sprite {
  id: string;
  imageData: string; // base64 encoded image
  pose: string;
  width: number;
  height: number;
  x?: number; // position in spritesheet
  y?: number; // position in spritesheet
}

export interface Character {
  id: string;
  name: string;
  description: string;
  baseSprite?: Sprite;
  sprites: Sprite[];
  colorPalette: ColorPalette;
  variations: CharacterVariation[];
  createdAt: Date;
  updatedAt: Date;
}

export interface CharacterVariation {
  id: string;
  name: string;
  description: string;
  sprites: Sprite[];
}

export interface ColorPalette {
  id: string;
  name: string;
  colors: string[]; // hex color codes
  isDefault: boolean;
}

// Animation and export types
export interface Animation {
  id: string;
  name: string;
  sprites: Sprite[];
  frameRate: number;
  loop: boolean;
}

export interface SpritesheetExport {
  imageData: string;
  metadata: SpritesheetMetadata;
}

export interface SpritesheetMetadata {
  width: number;
  height: number;
  spriteWidth: number;
  spriteHeight: number;
  sprites: SpriteMetadata[];
}

export interface SpriteMetadata {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

// AI and generation types
export interface GenerationRequest {
  prompt: string;
  pose: string;
  referenceImage?: string;
  colorPalette?: ColorPalette;
  style?: string;
  size?: { width: number; height: number };
}

export interface GenerationResponse {
  success: boolean;
  imageData?: string;
  error?: string;
}

// Predefined poses for sprite generation
export const SPRITE_POSES = [
  'idle',
  'walking',
  'running',
  'jumping',
  'attacking',
  'defending',
  'casting',
  'hurt',
  'dying',
  'celebrating',
  'crouching',
  'climbing'
] as const;

export type SpritePose = typeof SPRITE_POSES[number];

// UI workflow states
export type WorkflowStage = 'conception' | 'generation' | 'export';

export interface WorkflowState {
  stage: WorkflowStage;
  character?: Character;
  selectedPoses: SpritePose[];
  generatedSprites: Sprite[];
  isGenerating: boolean;
  error?: string;
}

// Image processing types
export interface ImageProcessingOptions {
  removeBackground: boolean;
  backgroundColor: string;
  tolerance: number;
  resize?: { width: number; height: number };
  pixelate?: boolean;
}

export interface ProcessedImage {
  originalData: string;
  processedData: string;
  width: number;
  height: number;
}
