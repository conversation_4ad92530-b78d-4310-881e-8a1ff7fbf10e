const express = require('express');
const cors = require('cors');
require('dotenv').config();
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3001;

// Helper function to generate mock sprite data
function generateMockSprite(pose, style) {
  // Create a simple colored square as a mock sprite
  // In a real implementation, this would call the Gemini API
  const colors = {
    idle: '#4A90E2',
    walking: '#7ED321',
    running: '#F5A623',
    jumping: '#D0021B',
    attacking: '#B91C1C',
    defending: '#6366F1',
    casting: '#8B5CF6',
    hurt: '#EF4444',
    dying: '#6B7280',
    celebrating: '#10B981',
    crouching: '#F59E0B',
    climbing: '#84CC16'
  };

  const color = colors[pose] || '#4A90E2';

  // Generate a simple SVG as base64 data URL
  const svg = `<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
    <rect width="32" height="32" fill="${color}"/>
    <text x="16" y="20" text-anchor="middle" fill="white" font-size="8" font-family="monospace">${pose.charAt(0).toUpperCase()}</text>
  </svg>`;

  const base64 = Buffer.from(svg).toString('base64');
  return `data:image/svg+xml;base64,${base64}`;
}

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '..', 'public', 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: 'SpriteGen API is running',
    timestamp: new Date().toISOString(),
    geminiConfigured: !!process.env.GEMINI_API_KEY
  });
});

// Generation endpoints
app.get('/api/generation/status', (req, res) => {
  res.json({
    status: 'ok',
    geminiConfigured: !!process.env.GEMINI_API_KEY,
    message: process.env.GEMINI_API_KEY ? 'Gemini API configured' : 'Gemini API key not configured'
  });
});

app.get('/api/generation/poses', (req, res) => {
  const poses = [
    'idle', 'walking', 'running', 'jumping', 'attacking', 'defending',
    'casting', 'hurt', 'dying', 'celebrating', 'crouching', 'climbing'
  ];
  res.json({ poses });
});

app.post('/api/generation/sprite', (req, res) => {
  const { prompt, pose, style, colorPalette } = req.body;

  if (!process.env.GEMINI_API_KEY) {
    return res.json({
      success: false,
      error: 'Gemini API key not configured'
    });
  }

  // Generate a mock sprite for now (placeholder for actual AI generation)
  const mockSpriteData = generateMockSprite(pose, style);

  res.json({
    success: true,
    imageData: mockSpriteData,
    pose: pose,
    metadata: {
      prompt: prompt,
      style: style,
      colorPalette: colorPalette,
      generatedAt: new Date().toISOString()
    }
  });
});

app.post('/api/generation/sprites/batch', (req, res) => {
  console.log('🎨 Batch sprite generation request received:', {
    basePrompt: req.body.basePrompt,
    poses: req.body.poses,
    style: req.body.style
  });

  const { basePrompt, poses, colorPalette, style } = req.body;

  if (!process.env.GEMINI_API_KEY) {
    console.log('❌ Gemini API key not configured');
    return res.json({
      success: false,
      error: 'Gemini API key not configured'
    });
  }

  if (!poses || !Array.isArray(poses) || poses.length === 0) {
    console.log('❌ No poses specified');
    return res.json({
      success: false,
      error: 'No poses specified for batch generation'
    });
  }

  // Generate mock sprites for each pose
  const results = poses.map(pose => {
    const mockSpriteData = generateMockSprite(pose, style);
    return {
      success: true,
      imageData: mockSpriteData,
      pose: pose,
      metadata: {
        prompt: `${basePrompt}, ${pose} pose`,
        style: style,
        colorPalette: colorPalette,
        generatedAt: new Date().toISOString()
      }
    };
  });

  console.log(`✅ Generated ${results.length} sprites successfully`);

  res.json({
    success: true,
    results: results,
    totalGenerated: results.length
  });
});

app.post('/api/generation/variations', (req, res) => {
  const { basePrompt, variationCount, pose, style, colorPalette } = req.body;

  if (!process.env.GEMINI_API_KEY) {
    return res.json({
      success: false,
      error: 'Gemini API key not configured'
    });
  }

  const count = variationCount || 3;
  const results = [];

  for (let i = 0; i < count; i++) {
    const mockSpriteData = generateMockSprite(pose || 'idle', style);
    results.push({
      success: true,
      imageData: mockSpriteData,
      pose: pose || 'idle',
      variation: i + 1,
      metadata: {
        prompt: `${basePrompt}, variation ${i + 1}`,
        style: style,
        colorPalette: colorPalette,
        generatedAt: new Date().toISOString()
      }
    });
  }

  res.json({
    success: true,
    results: results,
    totalGenerated: results.length
  });
});

app.post('/api/generation/enhance-prompt', (req, res) => {
  const { prompt } = req.body;
  // Simple prompt enhancement for now
  const enhancedPrompt = `${prompt}, pixel art style, game sprite, clean background, detailed character design`;
  res.json({
    success: true,
    originalPrompt: prompt,
    enhancedPrompt: enhancedPrompt
  });
});

// Image processing endpoints
app.post('/api/image/upload', upload.single('image'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No file uploaded' });
  }

  const imageUrl = `/uploads/${req.file.filename}`;
  res.json({
    success: true,
    imageUrl: imageUrl,
    filename: req.file.filename,
    originalName: req.file.originalname,
    size: req.file.size
  });
});

app.post('/api/image/info', (req, res) => {
  const { imageData } = req.body;
  
  // Mock image info for now
  res.json({
    success: true,
    info: {
      width: 64,
      height: 64,
      format: 'PNG',
      hasAlpha: true,
      colorDepth: 32
    }
  });
});

app.post('/api/image/remove-background', (req, res) => {
  // Mock response for now
  res.json({
    success: false,
    message: 'Background removal requires additional image processing libraries.',
    processedImage: null
  });
});

app.post('/api/image/resize', (req, res) => {
  // Mock response for now
  res.json({
    success: false,
    message: 'Image resizing requires additional image processing libraries.',
    resizedImage: null
  });
});

app.post('/api/image/spritesheet', (req, res) => {
  // Mock response for now
  res.json({
    success: false,
    message: 'Spritesheet generation requires additional image processing libraries.',
    spritesheet: null
  });
});

app.post('/api/image/pixelate', (req, res) => {
  // Mock response for now
  res.json({
    success: false,
    message: 'Pixelation requires additional image processing libraries.',
    pixelatedImage: null
  });
});

// Serve uploaded files
app.use('/uploads', express.static(uploadsDir));

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large' });
    }
  }
  
  console.error('Server error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

app.listen(PORT, () => {
  console.log(`🚀 SpriteGen API server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔑 Gemini API configured: ${!!process.env.GEMINI_API_KEY}`);
  
  if (!process.env.GEMINI_API_KEY) {
    console.log('⚠️  Warning: GEMINI_API_KEY not configured. AI features will be disabled.');
    console.log('   Please add your Gemini API key to the .env file to enable AI sprite generation.');
  }
});
