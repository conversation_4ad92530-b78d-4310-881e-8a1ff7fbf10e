import { ImageProcessingOptions, ProcessedImage, Sprite, SpritesheetExport, SpritesheetMetadata } from '../types';

/**
 * Remove background from an image using color tolerance
 */
export function removeBackground(
  imageData: string,
  backgroundColor: string = '#FF00FF', // Default magenta
  tolerance: number = 10
): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx!.drawImage(img, 0, 0);

      const imageData = ctx!.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      // Convert background color to RGB
      const bgColor = hexToRgb(backgroundColor);
      if (!bgColor) {
        reject(new Error('Invalid background color'));
        return;
      }

      // Remove background pixels
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];

        // Check if pixel is close to background color
        if (
          Math.abs(r - bgColor.r) <= tolerance &&
          Math.abs(g - bgColor.g) <= tolerance &&
          Math.abs(b - bgColor.b) <= tolerance
        ) {
          data[i + 3] = 0; // Set alpha to 0 (transparent)
        }
      }

      ctx!.putImageData(imageData, 0, 0);
      resolve(canvas.toDataURL('image/png'));
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = imageData;
  });
}

/**
 * Resize an image to specified dimensions
 */
export function resizeImage(
  imageData: string,
  width: number,
  height: number,
  maintainAspectRatio: boolean = true
): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      let newWidth = width;
      let newHeight = height;

      if (maintainAspectRatio) {
        const aspectRatio = img.width / img.height;
        if (width / height > aspectRatio) {
          newWidth = height * aspectRatio;
        } else {
          newHeight = width / aspectRatio;
        }
      }

      canvas.width = newWidth;
      canvas.height = newHeight;
      
      // Use nearest neighbor for pixel art
      ctx!.imageSmoothingEnabled = false;
      ctx!.drawImage(img, 0, 0, newWidth, newHeight);
      
      resolve(canvas.toDataURL('image/png'));
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = imageData;
  });
}

/**
 * Apply pixelation effect to an image
 */
export function pixelateImage(imageData: string, pixelSize: number = 4): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;

      // Disable smoothing for pixel art effect
      ctx!.imageSmoothingEnabled = false;

      // Draw image at reduced size
      const smallWidth = Math.ceil(img.width / pixelSize);
      const smallHeight = Math.ceil(img.height / pixelSize);
      
      ctx!.drawImage(img, 0, 0, smallWidth, smallHeight);
      
      // Scale back up
      ctx!.drawImage(canvas, 0, 0, smallWidth, smallHeight, 0, 0, img.width, img.height);
      
      resolve(canvas.toDataURL('image/png'));
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = imageData;
  });
}

/**
 * Process an image with specified options
 */
export async function processImage(
  imageData: string,
  options: ImageProcessingOptions
): Promise<ProcessedImage> {
  let processedData = imageData;
  
  // Remove background if requested
  if (options.removeBackground) {
    processedData = await removeBackground(
      processedData,
      options.backgroundColor,
      options.tolerance
    );
  }

  // Resize if requested
  if (options.resize) {
    processedData = await resizeImage(
      processedData,
      options.resize.width,
      options.resize.height
    );
  }

  // Apply pixelation if requested
  if (options.pixelate) {
    processedData = await pixelateImage(processedData);
  }

  // Get dimensions
  const dimensions = await getImageDimensions(processedData);

  return {
    originalData: imageData,
    processedData,
    width: dimensions.width,
    height: dimensions.height
  };
}

/**
 * Generate a spritesheet from multiple sprites
 */
export function generateSpritesheet(
  sprites: Sprite[],
  columns: number = 4,
  padding: number = 2
): Promise<SpritesheetExport> {
  return new Promise((resolve, reject) => {
    if (sprites.length === 0) {
      reject(new Error('No sprites provided'));
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    // Calculate spritesheet dimensions
    const spriteWidth = sprites[0].width;
    const spriteHeight = sprites[0].height;
    const rows = Math.ceil(sprites.length / columns);
    
    canvas.width = columns * spriteWidth + (columns - 1) * padding;
    canvas.height = rows * spriteHeight + (rows - 1) * padding;

    // Clear canvas with transparent background
    ctx!.clearRect(0, 0, canvas.width, canvas.height);

    const metadata: SpritesheetMetadata = {
      width: canvas.width,
      height: canvas.height,
      spriteWidth,
      spriteHeight,
      sprites: []
    };

    let loadedCount = 0;
    const totalSprites = sprites.length;

    sprites.forEach((sprite, index) => {
      const img = new Image();
      
      img.onload = () => {
        const col = index % columns;
        const row = Math.floor(index / columns);
        const x = col * (spriteWidth + padding);
        const y = row * (spriteHeight + padding);

        ctx!.drawImage(img, x, y);

        // Update sprite position
        sprite.x = x;
        sprite.y = y;

        // Add to metadata
        metadata.sprites.push({
          id: sprite.id,
          name: sprite.pose,
          x,
          y,
          width: spriteWidth,
          height: spriteHeight
        });

        loadedCount++;
        if (loadedCount === totalSprites) {
          resolve({
            imageData: canvas.toDataURL('image/png'),
            metadata
          });
        }
      };

      img.onerror = () => reject(new Error(`Failed to load sprite ${sprite.id}`));
      img.src = sprite.imageData;
    });
  });
}

/**
 * Get image dimensions from base64 data
 */
export function getImageDimensions(imageData: string): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = imageData;
  });
}

/**
 * Convert hex color to RGB
 */
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * Convert image to base64
 */
export function imageToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}
