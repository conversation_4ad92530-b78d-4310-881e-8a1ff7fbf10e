#!/usr/bin/env python3
"""
Animagine XL 4.0 Interface for SpriteGen
Provides AI sprite generation using the Animagine XL 4.0 model from Hugging Face
"""

import json
import sys
import torch
from diffusers import StableDiffusionXLPipeline
from PIL import Image
import base64
import io
import time
import os
import traceback

class AnimagineXLInterface:
    def __init__(self):
        self.pipeline = None
        self.model_loaded = False
        self.load_model()
    
    def load_model(self):
        """Load the Animagine XL 4.0 model from Hugging Face"""
        try:
            model_id = "cagliostrolab/animagine-xl-4.0"
            cache_dir = "./ai-models/cache"
            
            print("🎨 Loading Animagine XL 4.0 model...", file=sys.stderr)
            
            # Check if CUDA is available
            device = "cuda" if torch.cuda.is_available() else "cpu"
            print(f"🎮 Using device: {device}", file=sys.stderr)
            
            # Load the pipeline
            self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.float16 if device == "cuda" else torch.float32,
                use_safetensors=True,
                cache_dir=cache_dir
            )
            
            # Enable memory efficient attention
            if device == "cuda":
                self.pipeline.enable_attention_slicing()
                self.pipeline.enable_vae_slicing()
                
                # Enable xformers if available
                try:
                    self.pipeline.enable_xformers_memory_efficient_attention()
                    print("✅ XFormers memory efficient attention enabled", file=sys.stderr)
                except Exception as e:
                    print(f"⚠️ XFormers not available: {e}", file=sys.stderr)
            
            # Move to device
            self.pipeline.to(device)
            self.model_loaded = True
            
            print("ANIMAGINE_XL_READY", flush=True)
            
        except Exception as e:
            print(f"❌ Error loading model: {e}", file=sys.stderr)
            print(f"Traceback: {traceback.format_exc()}", file=sys.stderr)
            self.model_loaded = False
    
    def build_prompt(self, options):
        """Build optimized prompt for Animagine XL 4.0"""
        base_prompt = options.get('prompt', '')
        pose = options.get('pose', 'standing')
        style = options.get('style', 'anime')
        
        # Animagine XL specific prompt optimization
        prompt = f"{base_prompt}, {pose}, {style}, "
        prompt += "masterpiece, best quality, very aesthetic, absurdres, "
        prompt += "1girl, solo, full body, simple background, "
        prompt += "game character, sprite art, clean lines, "
        prompt += "detailed character design, anime style"
        
        # Add directional context if specified
        direction = options.get('direction')
        if direction:
            direction_map = {
                'north': 'facing away, back view',
                'south': 'facing forward, front view', 
                'east': 'facing right, side view',
                'west': 'facing left, side view',
                'northeast': 'three-quarter back right view',
                'northwest': 'three-quarter back left view',
                'southeast': 'three-quarter front right view',
                'southwest': 'three-quarter front left view'
            }
            prompt += f", {direction_map.get(direction, 'front view')}"
        
        return prompt
    
    def generate_sprite(self, options):
        """Generate sprite using Animagine XL 4.0"""
        if not self.model_loaded:
            return {
                "success": False,
                "error": "Model not loaded",
                "model": "animagine_xl_4.0"
            }
        
        try:
            prompt = self.build_prompt(options)
            negative_prompt = ("lowres, bad anatomy, bad hands, text, error, missing fingers, "
                             "extra digit, fewer digits, cropped, worst quality, low quality, "
                             "normal quality, jpeg artifacts, signature, watermark, username, "
                             "blurry, multiple views, reference sheet, nsfw")
            
            # Generation parameters
            width = options.get('width', 1024)
            height = options.get('height', 1024)
            num_inference_steps = options.get('num_inference_steps', 28)
            guidance_scale = options.get('guidance_scale', 12.0)
            
            print(f"🎨 Generating sprite: {prompt[:100]}...", file=sys.stderr)
            
            # Generate image
            with torch.autocast("cuda" if torch.cuda.is_available() else "cpu"):
                result = self.pipeline(
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_inference_steps=num_inference_steps,
                    guidance_scale=guidance_scale,
                    width=width,
                    height=height
                )
                image = result.images[0]
            
            # Resize to sprite dimensions if needed
            target_size = options.get('target_size', 512)
            if image.size[0] != target_size or image.size[1] != target_size:
                image = image.resize((target_size, target_size), Image.LANCZOS)
            
            # Convert to base64
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            return {
                "success": True,
                "imageData": f"data:image/png;base64,{image_base64}",
                "metadata": {
                    "model": "animagine_xl_4.0",
                    "prompt": prompt,
                    "negative_prompt": negative_prompt,
                    "timestamp": time.time(),
                    "resolution": f"{image.size[0]}x{image.size[1]}",
                    "inference_steps": num_inference_steps,
                    "guidance_scale": guidance_scale
                }
            }
            
        except Exception as e:
            print(f"❌ Error generating sprite: {e}", file=sys.stderr)
            print(f"Traceback: {traceback.format_exc()}", file=sys.stderr)
            return {
                "success": False,
                "error": str(e),
                "model": "animagine_xl_4.0"
            }

def main():
    """Main interface loop"""
    interface = AnimagineXLInterface()
    
    if not interface.model_loaded:
        print(json.dumps({
            "success": False,
            "error": "Failed to load Animagine XL 4.0 model",
            "model": "animagine_xl_4.0"
        }), flush=True)
        sys.exit(1)
    
    # Listen for requests
    for line in sys.stdin:
        try:
            options = json.loads(line.strip())
            result = interface.generate_sprite(options)
            print(json.dumps(result), flush=True)
        except json.JSONDecodeError as e:
            error_result = {
                "success": False,
                "error": f"Invalid JSON: {e}",
                "model": "animagine_xl_4.0"
            }
            print(json.dumps(error_result), flush=True)
        except Exception as e:
            error_result = {
                "success": False,
                "error": str(e),
                "model": "animagine_xl_4.0"
            }
            print(json.dumps(error_result), flush=True)

if __name__ == "__main__":
    main()
