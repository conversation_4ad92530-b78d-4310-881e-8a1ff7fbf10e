#!/bin/bash

# Animagine XL 4.0 Setup Script
echo "🚀 Setting up Animagine XL 4.0 environment..."

# Check if Python 3.8+ is available
python_version=$(python3 --version 2>&1 | grep -oP '\d+\.\d+' | head -1)
if [[ $(echo "$python_version < 3.8" | bc -l) -eq 0 ]]; then
    echo "❌ Python 3.8+ is required. Current version: $python_version"
    exit 1
fi

# Create virtual environment
echo "📦 Creating Python virtual environment..."
python3 -m venv animagine-xl-env

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source animagine-xl-env/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install PyTorch with CUDA support
echo "🔥 Installing PyTorch with CUDA 12.1 support..."
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# Install other dependencies
echo "📚 Installing other dependencies..."
pip install -r ai-models/animagine-xl-4.0/requirements.txt

# Set environment variables
echo "🌍 Setting up environment variables..."
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export XFORMERS_FORCE_DISABLE_TRITON=1
export HF_HOME=./ai-models/cache

# Create .env file for environment variables
cat > .env.animagine << EOF
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
XFORMERS_FORCE_DISABLE_TRITON=1
HF_HOME=./ai-models/cache
CUDA_VISIBLE_DEVICES=0
EOF

echo "✅ Animagine XL 4.0 environment setup complete!"
echo "📝 To activate the environment, run: source ai-models/animagine-xl-env/bin/activate"
echo "🔑 Don't forget to login to Hugging Face: huggingface-cli login"
