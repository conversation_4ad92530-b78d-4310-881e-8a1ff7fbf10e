#!/usr/bin/env python3
"""
Test script for Animagine XL 4.0 interface
"""

import json
import subprocess
import sys
import time

def test_animagine_interface():
    """Test the Animagine XL interface"""
    print("🧪 Testing Animagine XL 4.0 interface...")
    
    # Test options
    test_options = {
        "prompt": "knight warrior",
        "pose": "idle",
        "style": "pixel art",
        "direction": "south",
        "width": 512,
        "height": 512,
        "target_size": 64,
        "num_inference_steps": 20,
        "guidance_scale": 10.0
    }
    
    try:
        # Start the interface process
        process = subprocess.Popen(
            [sys.executable, "python-bridge/animagine_interface.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for model to load
        print("⏳ Waiting for model to load...")
        start_time = time.time()
        
        # Send test request
        request = json.dumps(test_options)
        stdout, stderr = process.communicate(input=request, timeout=300)  # 5 minute timeout
        
        # Parse response
        lines = stdout.strip().split('\n')
        
        # Look for the ready signal
        model_ready = False
        for line in lines:
            if line == "ANIMAGINE_XL_READY":
                model_ready = True
                print("✅ Model loaded successfully!")
                break
        
        # Look for the generation result
        result = None
        for line in lines:
            if line.startswith('{'):
                try:
                    result = json.loads(line)
                    break
                except json.JSONDecodeError:
                    continue
        
        if result:
            if result.get("success"):
                print("✅ Sprite generation successful!")
                print(f"📊 Metadata: {result.get('metadata', {})}")
                
                # Save test image
                if "imageData" in result:
                    import base64
                    from PIL import Image
                    import io
                    
                    # Decode base64 image
                    image_data = result["imageData"].split(",")[1]
                    image_bytes = base64.b64decode(image_data)
                    image = Image.open(io.BytesIO(image_bytes))
                    
                    # Save test image
                    image.save("test_sprite.png")
                    print("💾 Test sprite saved as test_sprite.png")
                
            else:
                print(f"❌ Generation failed: {result.get('error')}")
        else:
            print("❌ No valid response received")
        
        # Print stderr for debugging
        if stderr:
            print("🔍 Debug output:")
            print(stderr)
        
        elapsed_time = time.time() - start_time
        print(f"⏱️ Total time: {elapsed_time:.2f} seconds")
        
    except subprocess.TimeoutExpired:
        print("❌ Test timed out (5 minutes)")
        process.kill()
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_animagine_interface()
