import React, { useState } from 'react';
import { ColorPalette } from '../../types';

interface ColorPaletteEditorProps {
  palettes: ColorPalette[];
  selectedPalette: ColorPalette | null;
  onPaletteSelect: (palette: ColorPalette) => void;
  allowCustom?: boolean;
}

const ColorPaletteEditor: React.FC<ColorPaletteEditorProps> = ({
  palettes,
  selectedPalette,
  onPaletteSelect,
  allowCustom = true
}) => {
  const [isCreatingCustom, setIsCreatingCustom] = useState(false);
  const [customPaletteName, setCustomPaletteName] = useState('');
  const [customColors, setCustomColors] = useState<string[]>(['#000000', '#FFFFFF']);

  const handlePaletteClick = (palette: ColorPalette) => {
    onPaletteSelect(palette);
  };

  const handleAddCustomColor = () => {
    if (customColors.length < 16) {
      setCustomColors([...customColors, '#000000']);
    }
  };

  const handleRemoveCustomColor = (index: number) => {
    if (customColors.length > 2) {
      setCustomColors(customColors.filter((_, i) => i !== index));
    }
  };

  const handleCustomColorChange = (index: number, color: string) => {
    const newColors = [...customColors];
    newColors[index] = color;
    setCustomColors(newColors);
  };

  const handleCreateCustomPalette = () => {
    if (customPaletteName.trim() && customColors.length >= 2) {
      const customPalette: ColorPalette = {
        id: `custom_${Date.now()}`,
        name: customPaletteName.trim(),
        colors: customColors,
        isDefault: false
      };
      
      onPaletteSelect(customPalette);
      setIsCreatingCustom(false);
      setCustomPaletteName('');
      setCustomColors(['#000000', '#FFFFFF']);
    }
  };

  const handleCancelCustom = () => {
    setIsCreatingCustom(false);
    setCustomPaletteName('');
    setCustomColors(['#000000', '#FFFFFF']);
  };

  return (
    <div className="space-y-4">
      {/* Predefined Palettes */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {palettes.map((palette) => (
          <div
            key={palette.id}
            onClick={() => handlePaletteClick(palette)}
            className={`cursor-pointer p-4 rounded-lg border-2 transition-all ${
              selectedPalette?.id === palette.id
                ? 'border-blue-500 bg-blue-900 bg-opacity-30'
                : 'border-gray-600 bg-gray-700 hover:border-gray-500'
            }`}
          >
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-white">{palette.name}</h4>
              {selectedPalette?.id === palette.id && (
                <svg className="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            
            <div className="flex flex-wrap gap-1">
              {palette.colors.map((color, index) => (
                <div
                  key={index}
                  className="w-6 h-6 rounded border border-gray-500"
                  style={{ backgroundColor: color }}
                  title={color}
                />
              ))}
            </div>
            
            <div className="text-xs text-gray-400 mt-2">
              {palette.colors.length} colors
            </div>
          </div>
        ))}
      </div>

      {/* Custom Palette Creation */}
      {allowCustom && (
        <div className="border-t border-gray-600 pt-4">
          {!isCreatingCustom ? (
            <button
              onClick={() => setIsCreatingCustom(true)}
              className="w-full p-4 border-2 border-dashed border-gray-600 rounded-lg text-gray-400 hover:border-gray-500 hover:text-gray-300 transition-colors"
            >
              <svg className="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create Custom Palette
            </button>
          ) : (
            <div className="bg-gray-700 rounded-lg p-4 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Palette Name
                </label>
                <input
                  type="text"
                  value={customPaletteName}
                  onChange={(e) => setCustomPaletteName(e.target.value)}
                  placeholder="My Custom Palette"
                  className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Colors
                </label>
                <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 gap-2">
                  {customColors.map((color, index) => (
                    <div key={index} className="relative group">
                      <input
                        type="color"
                        value={color}
                        onChange={(e) => handleCustomColorChange(index, e.target.value)}
                        className="w-full h-10 rounded border border-gray-500 cursor-pointer"
                      />
                      {customColors.length > 2 && (
                        <button
                          onClick={() => handleRemoveCustomColor(index)}
                          className="absolute -top-2 -right-2 w-5 h-5 bg-red-600 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          ×
                        </button>
                      )}
                    </div>
                  ))}
                  
                  {customColors.length < 16 && (
                    <button
                      onClick={handleAddCustomColor}
                      className="w-full h-10 border-2 border-dashed border-gray-500 rounded text-gray-400 hover:border-gray-400 hover:text-gray-300 transition-colors"
                    >
                      +
                    </button>
                  )}
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleCreateCustomPalette}
                  disabled={!customPaletteName.trim() || customColors.length < 2}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-2 px-4 rounded transition-colors"
                >
                  Create Palette
                </button>
                <button
                  onClick={handleCancelCustom}
                  className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Selected Palette Info */}
      {selectedPalette && (
        <div className="bg-gray-700 rounded-lg p-4">
          <h4 className="font-medium text-white mb-2">Selected: {selectedPalette.name}</h4>
          <div className="flex flex-wrap gap-1">
            {selectedPalette.colors.map((color, index) => (
              <div
                key={index}
                className="flex items-center space-x-2 bg-gray-600 rounded px-2 py-1"
              >
                <div
                  className="w-4 h-4 rounded border border-gray-400"
                  style={{ backgroundColor: color }}
                />
                <span className="text-xs text-gray-300">{color}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ColorPaletteEditor;
