{"_class_name": "EulerDiscreteScheduler", "_diffusers_version": "0.29.0", "beta_end": 0.012, "beta_schedule": "scaled_linear", "beta_start": 0.00085, "clip_sample": false, "final_sigmas_type": "zero", "interpolation_type": "linear", "num_train_timesteps": 1000, "prediction_type": "epsilon", "rescale_betas_zero_snr": false, "sample_max_value": 1.0, "set_alpha_to_one": false, "sigma_max": null, "sigma_min": null, "skip_prk_steps": true, "steps_offset": 1, "timestep_spacing": "leading", "timestep_type": "discrete", "trained_betas": null, "use_karras_sigmas": false}