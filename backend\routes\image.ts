import express from 'express';
import fs from 'fs';
import path from 'path';
import { createCanvas, loadImage } from 'canvas';

const router = express.Router();

/**
 * Upload and process image
 */
router.post('/upload', (req: any, res) => {
  const upload = req.app.locals.upload;
  
  upload.single('image')(req, res, (err: any) => {
    if (err) {
      return res.status(400).json({ error: err.message });
    }

    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Convert uploaded file to base64
    const filePath = req.file.path;
    const fileBuffer = fs.readFileSync(filePath);
    const base64Data = `data:${req.file.mimetype};base64,${fileBuffer.toString('base64')}`;

    // Clean up uploaded file
    fs.unlinkSync(filePath);

    res.json({
      success: true,
      imageData: base64Data,
      metadata: {
        originalName: req.file.originalname,
        size: req.file.size,
        mimeType: req.file.mimetype,
        timestamp: new Date().toISOString()
      }
    });
  });
});

/**
 * Remove background from image
 */
router.post('/remove-background', async (req, res) => {
  try {
    const { imageData, backgroundColor = '#FF00FF', tolerance = 10 } = req.body;

    if (!imageData) {
      return res.status(400).json({ error: 'Image data is required' });
    }

    const processedImage = await removeBackgroundServer(imageData, backgroundColor, tolerance);

    res.json({
      success: true,
      imageData: processedImage,
      metadata: {
        backgroundColor,
        tolerance,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Background removal error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to remove background'
    });
  }
});

/**
 * Resize image
 */
router.post('/resize', async (req, res) => {
  try {
    const { imageData, width, height, maintainAspectRatio = true } = req.body;

    if (!imageData || !width || !height) {
      return res.status(400).json({ 
        error: 'Image data, width, and height are required' 
      });
    }

    const resizedImage = await resizeImageServer(imageData, width, height, maintainAspectRatio);

    res.json({
      success: true,
      imageData: resizedImage,
      metadata: {
        width,
        height,
        maintainAspectRatio,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Image resize error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to resize image'
    });
  }
});

/**
 * Generate spritesheet from multiple images
 */
router.post('/spritesheet', async (req, res) => {
  try {
    const { sprites, columns = 4, padding = 2, backgroundColor = 'transparent' } = req.body;

    if (!sprites || !Array.isArray(sprites) || sprites.length === 0) {
      return res.status(400).json({ 
        error: 'Sprites array is required and must not be empty' 
      });
    }

    const spritesheet = await generateSpritesheetServer(sprites, columns, padding, backgroundColor);

    res.json({
      success: true,
      imageData: spritesheet.imageData,
      metadata: spritesheet.metadata,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Spritesheet generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate spritesheet'
    });
  }
});

/**
 * Apply pixelation effect
 */
router.post('/pixelate', async (req, res) => {
  try {
    const { imageData, pixelSize = 4 } = req.body;

    if (!imageData) {
      return res.status(400).json({ error: 'Image data is required' });
    }

    const pixelatedImage = await pixelateImageServer(imageData, pixelSize);

    res.json({
      success: true,
      imageData: pixelatedImage,
      metadata: {
        pixelSize,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Pixelation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to pixelate image'
    });
  }
});

/**
 * Get image information
 */
router.post('/info', async (req, res) => {
  try {
    const { imageData } = req.body;

    if (!imageData) {
      return res.status(400).json({ error: 'Image data is required' });
    }

    const info = await getImageInfoServer(imageData);

    res.json({
      success: true,
      info,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Image info error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get image information'
    });
  }
});

// Server-side image processing functions using node-canvas

async function removeBackgroundServer(imageData: string, backgroundColor: string, tolerance: number): Promise<string> {
  const image = await loadImage(imageData);
  const canvas = createCanvas(image.width, image.height);
  const ctx = canvas.getContext('2d');

  ctx.drawImage(image, 0, 0);
  
  const imageDataObj = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageDataObj.data;

  // Convert background color to RGB
  const bgColor = hexToRgb(backgroundColor);
  if (!bgColor) throw new Error('Invalid background color');

  // Remove background pixels
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];

    if (
      Math.abs(r - bgColor.r) <= tolerance &&
      Math.abs(g - bgColor.g) <= tolerance &&
      Math.abs(b - bgColor.b) <= tolerance
    ) {
      data[i + 3] = 0; // Set alpha to 0 (transparent)
    }
  }

  ctx.putImageData(imageDataObj, 0, 0);
  return canvas.toDataURL('image/png');
}

async function resizeImageServer(imageData: string, width: number, height: number, maintainAspectRatio: boolean): Promise<string> {
  const image = await loadImage(imageData);
  
  let newWidth = width;
  let newHeight = height;

  if (maintainAspectRatio) {
    const aspectRatio = image.width / image.height;
    if (width / height > aspectRatio) {
      newWidth = height * aspectRatio;
    } else {
      newHeight = width / aspectRatio;
    }
  }

  const canvas = createCanvas(newWidth, newHeight);
  const ctx = canvas.getContext('2d');
  
  // Disable smoothing for pixel art
  ctx.imageSmoothingEnabled = false;
  ctx.drawImage(image, 0, 0, newWidth, newHeight);
  
  return canvas.toDataURL('image/png');
}

async function generateSpritesheetServer(sprites: any[], columns: number, padding: number, backgroundColor: string) {
  if (sprites.length === 0) throw new Error('No sprites provided');

  // Load first sprite to get dimensions
  const firstImage = await loadImage(sprites[0].imageData);
  const spriteWidth = firstImage.width;
  const spriteHeight = firstImage.height;
  const rows = Math.ceil(sprites.length / columns);
  
  const canvas = createCanvas(
    columns * spriteWidth + (columns - 1) * padding,
    rows * spriteHeight + (rows - 1) * padding
  );
  const ctx = canvas.getContext('2d');

  // Set background
  if (backgroundColor !== 'transparent') {
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }

  const metadata = {
    width: canvas.width,
    height: canvas.height,
    spriteWidth,
    spriteHeight,
    sprites: [] as any[]
  };

  // Draw sprites
  for (let i = 0; i < sprites.length; i++) {
    const sprite = sprites[i];
    const image = await loadImage(sprite.imageData);
    
    const col = i % columns;
    const row = Math.floor(i / columns);
    const x = col * (spriteWidth + padding);
    const y = row * (spriteHeight + padding);

    ctx.drawImage(image, x, y);

    metadata.sprites.push({
      id: sprite.id || `sprite_${i}`,
      name: sprite.name || sprite.pose || `sprite_${i}`,
      x,
      y,
      width: spriteWidth,
      height: spriteHeight
    });
  }

  return {
    imageData: canvas.toDataURL('image/png'),
    metadata
  };
}

async function pixelateImageServer(imageData: string, pixelSize: number): Promise<string> {
  const image = await loadImage(imageData);
  const canvas = createCanvas(image.width, image.height);
  const ctx = canvas.getContext('2d');

  ctx.imageSmoothingEnabled = false;

  // Draw at reduced size
  const smallWidth = Math.ceil(image.width / pixelSize);
  const smallHeight = Math.ceil(image.height / pixelSize);
  
  ctx.drawImage(image, 0, 0, smallWidth, smallHeight);
  
  // Scale back up
  const tempCanvas = createCanvas(smallWidth, smallHeight);
  const tempCtx = tempCanvas.getContext('2d');
  tempCtx.drawImage(canvas, 0, 0, smallWidth, smallHeight);
  
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  ctx.drawImage(tempCanvas, 0, 0, image.width, image.height);
  
  return canvas.toDataURL('image/png');
}

async function getImageInfoServer(imageData: string) {
  const image = await loadImage(imageData);
  
  return {
    width: image.width,
    height: image.height,
    format: 'PNG', // Assuming PNG for base64 data
    size: Math.round(imageData.length * 0.75) // Approximate size from base64
  };
}

function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

export default router;
