import { G<PERSON>Encoder, quantize, applyPalette } from 'gifenc';
import UPNG from 'upng-js';
import { Sprite, Animation } from '../types';

export interface AnimationExportOptions {
  frameRate: number;
  loop: boolean;
  quality?: 'low' | 'medium' | 'high';
  dithering?: boolean;
}

export interface AnimationFrame {
  imageData: string;
  delay: number; // in milliseconds
}

/**
 * Create GIF animation from sprites
 */
export async function createGifAnimation(
  sprites: Sprite[],
  options: AnimationExportOptions
): Promise<Blob> {
  const { frameRate, loop, quality = 'medium', dithering = false } = options;
  const delay = Math.round(1000 / frameRate);

  // Create GIF encoder
  const gif = GIFEncoder();

  // Load all sprite images
  const frames = await Promise.all(
    sprites.map(sprite => loadImageAsImageData(sprite.imageData))
  );

  if (frames.length === 0) {
    throw new Error('No frames to animate');
  }

  const { width, height } = frames[0];

  // Configure quality settings
  const quantizeOptions = {
    format: 'rgb565' as const,
    clearAlpha: false,
    clearAlphaColor: 0x00,
    clearAlphaThreshold: 0,
    oneBitAlpha: false
  };

  switch (quality) {
    case 'low':
      quantizeOptions.format = 'rgb444';
      break;
    case 'high':
      quantizeOptions.format = 'rgba4444';
      break;
  }

  // Process each frame
  for (const frame of frames) {
    const { data } = frame;
    
    // Quantize colors for GIF
    const palette = quantize(data, 256, quantizeOptions);
    const index = applyPalette(data, palette, quantizeOptions.format, dithering);

    // Add frame to GIF
    gif.writeFrame(index, width, height, {
      palette,
      delay,
      dispose: 2 // Restore to background
    });
  }

  // Finish GIF
  gif.finish();

  return new Blob([gif.bytes()], { type: 'image/gif' });
}

/**
 * Create APNG animation from sprites
 */
export async function createApngAnimation(
  sprites: Sprite[],
  options: AnimationExportOptions
): Promise<Blob> {
  const { frameRate, loop } = options;
  const delay = Math.round(1000 / frameRate);

  // Load all sprite images as RGBA arrays
  const frames = await Promise.all(
    sprites.map(sprite => loadImageAsRGBA(sprite.imageData))
  );

  if (frames.length === 0) {
    throw new Error('No frames to animate');
  }

  const { width, height, data: firstFrameData } = frames[0];

  // Prepare frame data for UPNG
  const frameBuffers = frames.map(frame => frame.data.buffer);
  const delays = new Array(frames.length).fill(delay);

  // Create APNG
  const apngBuffer = UPNG.encode(frameBuffers, width, height, 0, delays);

  return new Blob([apngBuffer], { type: 'image/png' });
}

/**
 * Create animation preview (for UI display)
 */
export function createAnimationPreview(
  sprites: Sprite[],
  frameRate: number = 8
): {
  frames: AnimationFrame[];
  totalDuration: number;
} {
  const delay = Math.round(1000 / frameRate);
  
  const frames: AnimationFrame[] = sprites.map(sprite => ({
    imageData: sprite.imageData,
    delay
  }));

  const totalDuration = frames.length * delay;

  return { frames, totalDuration };
}

/**
 * Optimize sprite sequence for animation
 */
export function optimizeAnimationSequence(sprites: Sprite[]): Sprite[] {
  // Remove duplicate consecutive frames
  const optimized: Sprite[] = [];
  
  for (let i = 0; i < sprites.length; i++) {
    const current = sprites[i];
    const previous = sprites[i - 1];
    
    // Skip if identical to previous frame
    if (!previous || current.imageData !== previous.imageData) {
      optimized.push(current);
    }
  }

  return optimized;
}

/**
 * Create sprite sheet optimized for animation
 */
export async function createAnimationSpritesheet(
  sprites: Sprite[],
  columns: number = 8
): Promise<{
  imageData: string;
  frameWidth: number;
  frameHeight: number;
  frameCount: number;
  columns: number;
  rows: number;
}> {
  if (sprites.length === 0) {
    throw new Error('No sprites provided');
  }

  const frameWidth = sprites[0].width;
  const frameHeight = sprites[0].height;
  const frameCount = sprites.length;
  const rows = Math.ceil(frameCount / columns);

  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    throw new Error('Could not get canvas context');
  }

  canvas.width = columns * frameWidth;
  canvas.height = rows * frameHeight;

  // Clear canvas with transparent background
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // Draw each sprite
  for (let i = 0; i < sprites.length; i++) {
    const sprite = sprites[i];
    const col = i % columns;
    const row = Math.floor(i / columns);
    const x = col * frameWidth;
    const y = row * frameHeight;

    const img = new Image();
    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
      img.src = sprite.imageData;
    });

    ctx.drawImage(img, x, y);
  }

  return {
    imageData: canvas.toDataURL('image/png'),
    frameWidth,
    frameHeight,
    frameCount,
    columns,
    rows
  };
}

/**
 * Load image as ImageData
 */
async function loadImageAsImageData(imageDataUrl: string): Promise<ImageData> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Could not get canvas context'));
        return;
      }

      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      resolve(imageData);
    };
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = imageDataUrl;
  });
}

/**
 * Load image as RGBA array
 */
async function loadImageAsRGBA(imageDataUrl: string): Promise<{
  data: Uint8Array;
  width: number;
  height: number;
}> {
  const imageData = await loadImageAsImageData(imageDataUrl);
  return {
    data: new Uint8Array(imageData.data),
    width: imageData.width,
    height: imageData.height
  };
}

/**
 * Calculate optimal frame rate for smooth animation
 */
export function calculateOptimalFrameRate(
  spriteCount: number,
  targetDuration: number = 1000 // 1 second default
): number {
  const frameRate = spriteCount / (targetDuration / 1000);
  
  // Clamp to reasonable values
  return Math.max(1, Math.min(30, Math.round(frameRate)));
}

/**
 * Generate animation metadata for game engines
 */
export function generateAnimationMetadata(
  animation: Animation,
  spritesheet: {
    frameWidth: number;
    frameHeight: number;
    columns: number;
    rows: number;
  }
) {
  const frames = animation.sprites.map((sprite, index) => {
    const col = index % spritesheet.columns;
    const row = Math.floor(index / spritesheet.columns);
    
    return {
      name: `${animation.name}_${index}`,
      frame: {
        x: col * spritesheet.frameWidth,
        y: row * spritesheet.frameHeight,
        w: spritesheet.frameWidth,
        h: spritesheet.frameHeight
      },
      duration: Math.round(1000 / animation.frameRate)
    };
  });

  return {
    meta: {
      app: 'SpriteGen',
      version: '1.0.0',
      format: 'RGBA8888',
      size: {
        w: spritesheet.columns * spritesheet.frameWidth,
        h: spritesheet.rows * spritesheet.frameHeight
      }
    },
    animations: {
      [animation.name]: {
        frames,
        frameRate: animation.frameRate,
        loop: animation.loop
      }
    }
  };
}
