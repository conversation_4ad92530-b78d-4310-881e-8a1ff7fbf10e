import React, { useState } from 'react';
import { UseSpriteReturn } from '../../hooks/useSprite';
import { UseImageProcessingReturn } from '../../hooks/useImageProcessing';
import { downloadFile } from '../../utils/exportUtils';

interface ExportStageProps {
  spriteHook: UseSpriteReturn;
  imageProcessingHook: UseImageProcessingReturn;
  onBack: () => void;
  onRestart: () => void;
}

const ExportStage: React.FC<ExportStageProps> = ({
  spriteHook,
  imageProcessingHook,
  onBack,
  onRestart
}) => {
  const [exportFormat, setExportFormat] = useState<'png' | 'spritesheet' | 'gif'>('spritesheet');
  const [isExporting, setIsExporting] = useState(false);

  const { sprites, currentCharacter } = spriteHook;
  const { processSpritesheet } = imageProcessingHook;

  const handleExport = async () => {
    if (sprites.length === 0) return;

    setIsExporting(true);
    try {
      switch (exportFormat) {
        case 'png':
          // Export individual PNGs
          sprites.forEach((sprite, index) => {
            const link = document.createElement('a');
            link.href = sprite.imageData;
            link.download = `${currentCharacter?.name || 'sprite'}_${sprite.pose}_${index + 1}.png`;
            link.click();
          });
          break;

        case 'spritesheet':
          // Export as spritesheet
          const spritesheet = await processSpritesheet(sprites, 4, 2);
          if (spritesheet) {
            const blob = await fetch(spritesheet.imageData).then(r => r.blob());
            downloadFile(blob, `${currentCharacter?.name || 'spritesheet'}.png`);
            
            // Also download metadata
            const metadata = JSON.stringify(spritesheet.metadata, null, 2);
            const metadataBlob = new Blob([metadata], { type: 'application/json' });
            downloadFile(metadataBlob, `${currentCharacter?.name || 'spritesheet'}_metadata.json`);
          }
          break;

        case 'gif':
          // For now, just export as individual PNGs
          // GIF export would require the animation utilities
          sprites.forEach((sprite, index) => {
            const link = document.createElement('a');
            link.href = sprite.imageData;
            link.download = `${currentCharacter?.name || 'sprite'}_${sprite.pose}_${index + 1}.png`;
            link.click();
          });
          break;
      }
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-4">Export Your Sprites</h2>
        <p className="text-gray-400 max-w-2xl mx-auto">
          Choose your export format and download your generated sprites. All formats include metadata for game engine compatibility.
        </p>
      </div>

      {/* Sprite Preview */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">
          Generated Sprites ({sprites.length})
        </h3>
        
        <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4">
          {sprites.map((sprite) => (
            <div key={sprite.id} className="text-center">
              <div className="bg-gray-700 rounded-lg p-2 mb-2">
                <img
                  src={sprite.imageData}
                  alt={sprite.pose}
                  className="w-full h-auto pixelated"
                  style={{ imageRendering: 'pixelated' }}
                />
              </div>
              <div className="text-xs text-gray-400 capitalize">{sprite.pose}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Export Options */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Export Format</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => setExportFormat('png')}
            className={`p-4 rounded-lg border-2 transition-all text-left ${
              exportFormat === 'png'
                ? 'border-blue-500 bg-blue-900 bg-opacity-30'
                : 'border-gray-600 bg-gray-700 hover:border-gray-500'
            }`}
          >
            <h4 className="font-semibold text-white mb-2">Individual PNGs</h4>
            <p className="text-sm text-gray-400">
              Download each sprite as a separate PNG file with transparency.
            </p>
          </button>

          <button
            onClick={() => setExportFormat('spritesheet')}
            className={`p-4 rounded-lg border-2 transition-all text-left ${
              exportFormat === 'spritesheet'
                ? 'border-blue-500 bg-blue-900 bg-opacity-30'
                : 'border-gray-600 bg-gray-700 hover:border-gray-500'
            }`}
          >
            <h4 className="font-semibold text-white mb-2">Spritesheet</h4>
            <p className="text-sm text-gray-400">
              Combined spritesheet with metadata for game engines.
            </p>
          </button>

          <button
            onClick={() => setExportFormat('gif')}
            className={`p-4 rounded-lg border-2 transition-all text-left ${
              exportFormat === 'gif'
                ? 'border-blue-500 bg-blue-900 bg-opacity-30'
                : 'border-gray-600 bg-gray-700 hover:border-gray-500'
            }`}
          >
            <h4 className="font-semibold text-white mb-2">Animated GIF</h4>
            <p className="text-sm text-gray-400">
              Animated GIF for previews and web use.
            </p>
          </button>
        </div>
      </div>

      {/* Character Summary */}
      {currentCharacter && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-semibold text-white mb-4">Character Summary</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-white mb-2">Character Details</h4>
              <div className="space-y-2 text-sm">
                <div><span className="text-gray-400">Name:</span> <span className="text-white">{currentCharacter.name}</span></div>
                <div><span className="text-gray-400">Sprites:</span> <span className="text-white">{sprites.length} poses</span></div>
                <div><span className="text-gray-400">Created:</span> <span className="text-white">{currentCharacter.createdAt.toLocaleDateString()}</span></div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-white mb-2">Color Palette</h4>
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-sm text-gray-400">{currentCharacter.colorPalette.name}:</span>
                <div className="flex space-x-1">
                  {currentCharacter.colorPalette.colors.slice(0, 8).map((color, index) => (
                    <div
                      key={index}
                      className="w-4 h-4 rounded border border-gray-500"
                      style={{ backgroundColor: color }}
                      title={color}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between">
        <button
          onClick={onBack}
          className="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-semibold transition-colors"
        >
          ← Back to Generation
        </button>

        <div className="space-x-4">
          <button
            onClick={onRestart}
            className="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-semibold transition-colors"
          >
            Create New Character
          </button>

          <button
            onClick={handleExport}
            disabled={sprites.length === 0 || isExporting}
            className={`px-8 py-3 rounded-lg font-semibold transition-colors ${
              sprites.length > 0 && !isExporting
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-gray-600 text-gray-400 cursor-not-allowed'
            }`}
          >
            {isExporting ? 'Exporting...' : `Export ${exportFormat.toUpperCase()}`}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExportStage;
