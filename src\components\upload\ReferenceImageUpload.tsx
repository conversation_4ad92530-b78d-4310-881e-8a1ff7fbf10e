import React, { useCallback, useState } from 'react';

interface ReferenceImageUploadProps {
  onUpload: (files: File[]) => void;
  images: string[];
  onRemove: (index: number) => void;
  maxImages?: number;
  maxFileSize?: number; // in bytes
}

const ReferenceImageUpload: React.FC<ReferenceImageUploadProps> = ({
  onUpload,
  images,
  onRemove,
  maxImages = 5,
  maxFileSize = 10 * 1024 * 1024 // 10MB
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const validateFiles = (files: File[]): { valid: File[]; errors: string[] } => {
    const valid: File[] = [];
    const errors: string[] = [];

    for (const file of files) {
      // Check file type
      if (!file.type.startsWith('image/')) {
        errors.push(`${file.name} is not an image file`);
        continue;
      }

      // Check file size
      if (file.size > maxFileSize) {
        errors.push(`${file.name} is too large (max ${Math.round(maxFileSize / 1024 / 1024)}MB)`);
        continue;
      }

      // Check if we're at max images
      if (images.length + valid.length >= maxImages) {
        errors.push(`Maximum ${maxImages} images allowed`);
        break;
      }

      valid.push(file);
    }

    return { valid, errors };
  };

  const handleFileSelect = useCallback((files: File[]) => {
    setUploadError(null);
    
    const { valid, errors } = validateFiles(files);
    
    if (errors.length > 0) {
      setUploadError(errors.join(', '));
    }
    
    if (valid.length > 0) {
      onUpload(valid);
    }
  }, [onUpload, images.length, maxImages, maxFileSize]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    handleFileSelect(files);
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    handleFileSelect(files);
    
    // Reset input
    e.target.value = '';
  }, [handleFileSelect]);

  const canUploadMore = images.length < maxImages;

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      {canUploadMore && (
        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            isDragOver
              ? 'border-blue-400 bg-blue-900 bg-opacity-20'
              : 'border-gray-600 hover:border-gray-500'
          }`}
        >
          <input
            type="file"
            multiple
            accept="image/*"
            onChange={handleFileInput}
            className="hidden"
            id="reference-upload"
          />
          
          <label htmlFor="reference-upload" className="cursor-pointer">
            <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            
            <div className="text-gray-300">
              <p className="text-lg font-medium mb-2">
                Drop images here or click to upload
              </p>
              <p className="text-sm text-gray-400">
                PNG, JPG, GIF up to {Math.round(maxFileSize / 1024 / 1024)}MB
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {images.length} of {maxImages} images uploaded
              </p>
            </div>
          </label>
        </div>
      )}

      {/* Upload Error */}
      {uploadError && (
        <div className="bg-red-900 border border-red-700 rounded-lg p-3">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <span className="text-red-200 text-sm">{uploadError}</span>
          </div>
        </div>
      )}

      {/* Uploaded Images */}
      {images.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-300">Reference Images</h4>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {images.map((image, index) => (
              <div key={index} className="relative group">
                <div className="aspect-square bg-gray-700 rounded-lg overflow-hidden">
                  <img
                    src={image}
                    alt={`Reference ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Remove Button */}
                <button
                  onClick={() => onRemove(index)}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-600 text-white rounded-full text-sm opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-700"
                  title="Remove image"
                >
                  ×
                </button>
                
                {/* Image Info */}
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  Reference {index + 1}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Max Images Reached */}
      {!canUploadMore && (
        <div className="bg-yellow-900 border border-yellow-700 rounded-lg p-3">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span className="text-yellow-200 text-sm">
              Maximum {maxImages} images reached. Remove an image to upload more.
            </span>
          </div>
        </div>
      )}

      {/* Tips */}
      {images.length === 0 && (
        <div className="bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-4">
          <h4 className="text-blue-300 font-medium mb-2">💡 Reference Image Tips</h4>
          <ul className="text-blue-200 text-sm space-y-1">
            <li>• Upload clear, high-quality images of your character</li>
            <li>• Multiple angles help the AI understand the design better</li>
            <li>• Consistent lighting and style work best</li>
            <li>• Avoid cluttered backgrounds if possible</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default ReferenceImageUpload;
