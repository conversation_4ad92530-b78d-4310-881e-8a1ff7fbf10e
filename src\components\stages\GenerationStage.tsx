import React, { useState, useEffect } from 'react';
import { SpritePose, SPRITE_POSES } from '../../types';
import { UseSpriteReturn } from '../../hooks/useSprite';
import { UseImageProcessingReturn } from '../../hooks/useImageProcessing';

interface GenerationStageProps {
  spriteHook: UseSpriteReturn;
  imageProcessingHook: UseImageProcessingReturn;
  onNext: () => void;
  onBack: () => void;
}

const GenerationStage: React.FC<GenerationStageProps> = ({
  spriteHook,
  imageProcessingHook,
  onNext,
  onBack
}) => {
  const [selectedPoses, setSelectedPoses] = useState<SpritePose[]>([]);

  const {
    currentCharacter,
    sprites,
    generateMultipleSprites,
    availablePoses,
    isGenerating
  } = spriteHook;

  useEffect(() => {
    // Set default poses if none selected
    if (selectedPoses.length === 0) {
      setSelectedPoses(['idle', 'walking', 'jumping', 'attacking']);
    }
  }, [selectedPoses.length]);

  const handlePoseToggle = (pose: SpritePose) => {
    setSelectedPoses(prev => 
      prev.includes(pose)
        ? prev.filter(p => p !== pose)
        : [...prev, pose]
    );
  };

  const handleGenerateSprites = async () => {
    console.log('🎮 Generate sprites button clicked');
    console.log('Current character:', currentCharacter);
    console.log('Selected poses:', selectedPoses);

    if (!currentCharacter || selectedPoses.length === 0) {
      console.log('❌ Cannot generate: missing character or poses');
      return;
    }

    console.log('🚀 Starting sprite generation...');
    try {
      const result = await generateMultipleSprites({
        basePrompt: currentCharacter.description,
        poses: selectedPoses,
        colorPalette: currentCharacter.colorPalette,
        style: 'pixel art'
      });
      console.log('✅ Sprite generation result:', result);
    } catch (error) {
      console.error('❌ Failed to generate sprites:', error);
    }
    console.log('🏁 Sprite generation finished');
  };

  const canProceed = sprites.length > 0;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-4">Sprite Generation</h2>
        <p className="text-gray-400 max-w-2xl mx-auto">
          Select the poses you want to generate for {currentCharacter?.name}. The AI will create consistent sprites for each pose.
        </p>
      </div>

      {/* Character Info */}
      {currentCharacter && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-semibold text-white mb-2">{currentCharacter.name}</h3>
          <p className="text-gray-300 mb-4">{currentCharacter.description}</p>
          
          {/* Color Palette Preview */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Color Palette:</span>
            <div className="flex space-x-1">
              {currentCharacter.colorPalette.colors.slice(0, 8).map((color, index) => (
                <div
                  key={index}
                  className="w-4 h-4 rounded border border-gray-500"
                  style={{ backgroundColor: color }}
                  title={color}
                />
              ))}
            </div>
            <span className="text-sm text-gray-400">({currentCharacter.colorPalette.name})</span>
          </div>
        </div>
      )}

      {/* Pose Selection */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Select Poses</h3>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {SPRITE_POSES.map((pose) => (
            <button
              key={pose}
              onClick={() => handlePoseToggle(pose)}
              className={`p-4 rounded-lg border-2 transition-all text-left ${
                selectedPoses.includes(pose)
                  ? 'border-blue-500 bg-blue-900 bg-opacity-30 text-white'
                  : 'border-gray-600 bg-gray-700 text-gray-300 hover:border-gray-500'
              }`}
            >
              <div className="font-medium capitalize mb-1">{pose}</div>
              <div className="text-xs text-gray-400">
                {getPoseDescription(pose)}
              </div>
            </button>
          ))}
        </div>

        <div className="mt-4 text-sm text-gray-400">
          Selected: {selectedPoses.length} poses
        </div>
      </div>

      {/* Generated Sprites */}
      {sprites.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-semibold text-white mb-4">Generated Sprites</h3>
          
          <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4">
            {sprites.map((sprite) => (
              <div key={sprite.id} className="text-center">
                <div className="bg-gray-700 rounded-lg p-2 mb-2">
                  <img
                    src={sprite.imageData}
                    alt={sprite.pose}
                    className="w-full h-auto pixelated"
                    style={{ imageRendering: 'pixelated' }}
                  />
                </div>
                <div className="text-xs text-gray-400 capitalize">{sprite.pose}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between">
        <button
          onClick={onBack}
          className="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-semibold transition-colors"
        >
          ← Back to Conception
        </button>

        <div className="space-x-4">
          <button
            onClick={handleGenerateSprites}
            disabled={selectedPoses.length === 0 || isGenerating}
            className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
              selectedPoses.length > 0 && !isGenerating
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-gray-600 text-gray-400 cursor-not-allowed'
            }`}
          >
            {isGenerating ? 'Generating...' : `Generate ${selectedPoses.length} Sprites`}
          </button>

          <button
            onClick={onNext}
            disabled={!canProceed}
            className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
              canProceed
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-gray-600 text-gray-400 cursor-not-allowed'
            }`}
          >
            Continue to Export →
          </button>
        </div>
      </div>
    </div>
  );
};

function getPoseDescription(pose: SpritePose): string {
  const descriptions: Record<SpritePose, string> = {
    idle: 'Standing still',
    walking: 'Moving forward',
    running: 'Fast movement',
    jumping: 'In the air',
    attacking: 'Combat action',
    defending: 'Protective stance',
    casting: 'Magic/spell',
    hurt: 'Taking damage',
    dying: 'Death animation',
    celebrating: 'Victory pose',
    crouching: 'Low position',
    climbing: 'Vertical movement'
  };

  return descriptions[pose] || 'Custom pose';
}

export default GenerationStage;
