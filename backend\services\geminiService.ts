import { GoogleGenerativeAI } from '@google/generative-ai';
import { createCanvas } from 'canvas';

// Enable fetch using cross-fetch polyfill
async function enableFetch(): Promise<boolean> {
  try {
    // Import cross-fetch which provides a complete fetch implementation
    const fetch = require('cross-fetch');

    // Set up globals for Gemini API
    globalThis.fetch = fetch;
    globalThis.Headers = fetch.Headers;
    globalThis.Request = fetch.Request;
    globalThis.Response = fetch.Response;

    console.log('✅ Fetch enabled via cross-fetch polyfill');

    const available = typeof globalThis.fetch === 'function' && typeof globalThis.Headers === 'function';
    console.log(`🔍 Final fetch check: fetch=${typeof globalThis.fetch}, Headers=${typeof globalThis.Headers}, available=${available}`);
    return available;
  } catch (error) {
    console.error('❌ Failed to enable fetch:', error);
    return false;
  }
}

interface GenerationOptions {
  prompt: string;
  referenceImages?: string[];
  style?: string;
  size?: { width: number; height: number };
  pose?: string;
  colorPalette?: any;
}

interface GenerationResult {
  success: boolean;
  imageData?: string;
  animationFrames?: string[]; // Array of base64 image data for 8-frame animation
  error?: string;
  enhancedPrompt?: string;
  frameCount?: number;
  metadata?: {
    aiGenerated: boolean;
    model?: string;
    timestamp?: number;
    [key: string]: any;
  };
}

interface SpriteCharacteristics {
  bodyType: string;
  colors: string[];
  features: string[];
  pose: string;
  style: string;
}

interface RateLimitTracker {
  requestsPerMinute: number;
  requestsPerDay: number;
  tokensPerMinute: number;
  lastMinuteReset: number;
  lastDayReset: number;
  consecutiveErrors: number;
  lastErrorTime: number;
  retryAfter?: number;
}

interface RateLimitConfig {
  maxRequestsPerMinute: number;
  maxRequestsPerDay: number;
  maxTokensPerMinute: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
}

class GeminiService {
  private genAI: GoogleGenerativeAI;
  private textModel: any;
  private imageModel: any;
  private fetchAvailable: boolean = false;
  private initPromise: Promise<void>;

  // Rate limiting properties
  private rateLimitTracker!: RateLimitTracker;
  private rateLimitConfig!: RateLimitConfig;
  private requestQueue: Array<{ resolve: Function; reject: Function; timestamp: number }> = [];

  // Sequential generation management
  private isProcessingQueue: boolean = false;
  private lastRequestTime: number = 0;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY environment variable is required');
    }

    this.genAI = new GoogleGenerativeAI(apiKey);
    this.textModel = this.genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });
    this.imageModel = this.genAI.getGenerativeModel({ model: 'gemini-2.5-flash-image-preview' });

    // Initialize rate limiting (conservative free tier limits)
    this.initializeRateLimiting();

    // Initialize fetch asynchronously
    this.initPromise = this.initializeFetch();
  }

  private async initializeFetch(): Promise<void> {
    this.fetchAvailable = await enableFetch();

    if (this.fetchAvailable) {
      console.log('✅ Fetch API available - Gemini integration enabled');
    } else {
      console.log('⚠️ Fetch API not available - Using fallback analysis');
    }
  }

  private async ensureInitialized(): Promise<void> {
    await this.initPromise;
  }

  /**
   * Initialize rate limiting with conservative free tier limits
   */
  private initializeRateLimiting(): void {
    const now = Date.now();

    // Actual Free Tier limits for Gemini 2.5 Flash Image Preview
    this.rateLimitConfig = {
      maxRequestsPerMinute: 10,   // Free Tier: 10 RPM for Gemini 2.5 Flash Image Preview
      maxRequestsPerDay: 500,     // Free Tier: 500 RPD for Gemini 2.5 Flash Image Preview
      maxTokensPerMinute: 250000, // Free Tier: 250,000 TPM
      baseDelayMs: 6000,          // 6 second base delay (10 requests/60s = 6s spacing)
      maxDelayMs: 300000,         // 5 minutes max delay
      backoffMultiplier: 2        // Exponential backoff multiplier
    };

    this.rateLimitTracker = {
      requestsPerMinute: 0,
      requestsPerDay: 0,
      tokensPerMinute: 0,
      lastMinuteReset: now,
      lastDayReset: now,
      consecutiveErrors: 0,
      lastErrorTime: 0
    };

    console.log('🚦 Rate limiting initialized (Free Tier):', {
      rpm: this.rateLimitConfig.maxRequestsPerMinute,
      rpd: this.rateLimitConfig.maxRequestsPerDay,
      tpm: this.rateLimitConfig.maxTokensPerMinute,
      sequentialSpacing: `${this.rateLimitConfig.baseDelayMs/1000}s`
    });
  }

  /**
   * Check if we can make a request without hitting rate limits
   */
  private canMakeRequest(): { allowed: boolean; waitTime?: number; reason?: string } {
    const now = Date.now();

    // Reset counters if time windows have passed
    this.resetCountersIfNeeded(now);

    // Check requests per minute
    if (this.rateLimitTracker.requestsPerMinute >= this.rateLimitConfig.maxRequestsPerMinute) {
      const waitTime = 60000 - (now - this.rateLimitTracker.lastMinuteReset);
      return {
        allowed: false,
        waitTime: Math.max(waitTime, 0),
        reason: `RPM limit reached (${this.rateLimitTracker.requestsPerMinute}/${this.rateLimitConfig.maxRequestsPerMinute})`
      };
    }

    // Check requests per day
    if (this.rateLimitTracker.requestsPerDay >= this.rateLimitConfig.maxRequestsPerDay) {
      const waitTime = 86400000 - (now - this.rateLimitTracker.lastDayReset); // 24 hours
      return {
        allowed: false,
        waitTime: Math.max(waitTime, 0),
        reason: `RPD limit reached (${this.rateLimitTracker.requestsPerDay}/${this.rateLimitConfig.maxRequestsPerDay})`
      };
    }

    // Check if we're in a backoff period from previous errors
    if (this.rateLimitTracker.retryAfter && now < this.rateLimitTracker.retryAfter) {
      const waitTime = this.rateLimitTracker.retryAfter - now;
      return {
        allowed: false,
        waitTime,
        reason: `In backoff period (${Math.ceil(waitTime/1000)}s remaining)`
      };
    }

    return { allowed: true };
  }

  /**
   * Reset rate limit counters when time windows expire
   */
  private resetCountersIfNeeded(now: number): void {
    // Reset minute counter
    if (now - this.rateLimitTracker.lastMinuteReset >= 60000) {
      this.rateLimitTracker.requestsPerMinute = 0;
      this.rateLimitTracker.tokensPerMinute = 0;
      this.rateLimitTracker.lastMinuteReset = now;
    }

    // Reset day counter (Pacific time reset)
    const pacificOffset = -8 * 60 * 60 * 1000; // PST offset
    const pacificNow = new Date(now + pacificOffset);
    const pacificLastReset = new Date(this.rateLimitTracker.lastDayReset + pacificOffset);

    if (pacificNow.getDate() !== pacificLastReset.getDate()) {
      this.rateLimitTracker.requestsPerDay = 0;
      this.rateLimitTracker.lastDayReset = now;
      console.log('🔄 Daily rate limit counter reset');
    }
  }

  /**
   * Handle rate limit errors and implement exponential backoff
   */
  private handleRateLimitError(error: any): { shouldRetry: boolean; retryAfter: number } {
    const now = Date.now();
    this.rateLimitTracker.consecutiveErrors++;
    this.rateLimitTracker.lastErrorTime = now;

    let retryAfter = 0;

    // Parse retry delay from error response
    if (error.errorDetails) {
      for (const detail of error.errorDetails) {
        if (detail['@type'] === 'type.googleapis.com/google.rpc.RetryInfo' && detail.retryDelay) {
          const delayStr = detail.retryDelay;
          const delayMatch = delayStr.match(/(\d+)s/);
          if (delayMatch) {
            retryAfter = parseInt(delayMatch[1]) * 1000;
            console.log(`🕐 API specified retry delay: ${delayMatch[1]}s`);
          }
        }
      }
    }

    // If no retry delay specified, use exponential backoff
    if (retryAfter === 0) {
      const backoffDelay = Math.min(
        this.rateLimitConfig.baseDelayMs * Math.pow(this.rateLimitConfig.backoffMultiplier, this.rateLimitTracker.consecutiveErrors - 1),
        this.rateLimitConfig.maxDelayMs
      );
      retryAfter = backoffDelay;
      console.log(`⏰ Using exponential backoff: ${Math.ceil(retryAfter/1000)}s (attempt ${this.rateLimitTracker.consecutiveErrors})`);
    }

    // Set retry after time
    this.rateLimitTracker.retryAfter = now + retryAfter;

    // Log rate limit status
    this.logRateLimitStatus(error);

    // Decide if we should retry (max 3 attempts)
    const shouldRetry = this.rateLimitTracker.consecutiveErrors <= 3;

    return { shouldRetry, retryAfter };
  }

  /**
   * Log detailed rate limit status with complete error information
   */
  private logRateLimitStatus(error?: any): void {
    const now = Date.now();
    const status = {
      rpm: `${this.rateLimitTracker.requestsPerMinute}/${this.rateLimitConfig.maxRequestsPerMinute}`,
      rpd: `${this.rateLimitTracker.requestsPerDay}/${this.rateLimitConfig.maxRequestsPerDay}`,
      consecutiveErrors: this.rateLimitTracker.consecutiveErrors,
      retryAfter: this.rateLimitTracker.retryAfter ? Math.ceil((this.rateLimitTracker.retryAfter - now) / 1000) : 0
    };

    if (error) {
      console.log('🚫 Rate limit error details:', {
        status,
        errorType: error.status,
        errorStatusText: error.statusText,
        errorMessage: error.message // Full untruncated error message
      });

      // Log additional error details if available
      if (error.errorDetails && Array.isArray(error.errorDetails)) {
        console.log('📋 Additional error details:');
        error.errorDetails.forEach((detail: any, index: number) => {
          console.log(`   ${index + 1}. Type: ${detail['@type']}`);

          // Log quota failure details
          if (detail['@type'] === 'type.googleapis.com/google.rpc.QuotaFailure' && detail.violations) {
            console.log('      Quota violations:');
            detail.violations.forEach((violation: any, vIndex: number) => {
              console.log(`         ${vIndex + 1}. Metric: ${violation.quotaMetric}`);
              console.log(`            Quota ID: ${violation.quotaId}`);
              if (violation.quotaDimensions) {
                console.log(`            Dimensions:`, violation.quotaDimensions);
              }
            });
          }

          // Log retry info details
          if (detail['@type'] === 'type.googleapis.com/google.rpc.RetryInfo' && detail.retryDelay) {
            console.log(`      Retry delay: ${detail.retryDelay}`);
          }

          // Log help links
          if (detail['@type'] === 'type.googleapis.com/google.rpc.Help' && detail.links) {
            console.log('      Help links:');
            detail.links.forEach((link: any, lIndex: number) => {
              console.log(`         ${lIndex + 1}. ${link.description}: ${link.url}`);
            });
          }
        });
      }
    } else {
      console.log('📊 Current rate limit status:', status);
    }
  }

  /**
   * Record successful request
   */
  private recordSuccessfulRequest(estimatedTokens: number = 1000): void {
    this.rateLimitTracker.requestsPerMinute++;
    this.rateLimitTracker.requestsPerDay++;
    this.rateLimitTracker.tokensPerMinute += estimatedTokens;
    this.rateLimitTracker.consecutiveErrors = 0; // Reset error count on success

    console.log(`✅ Request recorded: RPM ${this.rateLimitTracker.requestsPerMinute}/${this.rateLimitConfig.maxRequestsPerMinute}, RPD ${this.rateLimitTracker.requestsPerDay}/${this.rateLimitConfig.maxRequestsPerDay}`);
  }

  /**
   * Wait for rate limit clearance with intelligent queuing and sequential processing
   */
  private async waitForRateLimit(): Promise<void> {
    const check = this.canMakeRequest();

    if (check.allowed) {
      // Ensure minimum spacing between requests for sequential processing
      await this.enforceSequentialSpacing();
      return;
    }

    console.log(`⏳ Rate limit wait: ${check.reason}, waiting ${Math.ceil((check.waitTime || 0)/1000)}s`);

    if (check.waitTime && check.waitTime > 0) {
      await new Promise(resolve => setTimeout(resolve, check.waitTime));
    }
  }

  /**
   * Enforce minimum spacing between requests for sequential processing
   */
  private async enforceSequentialSpacing(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    const minSpacing = this.rateLimitConfig.baseDelayMs;

    if (timeSinceLastRequest < minSpacing) {
      const waitTime = minSpacing - timeSinceLastRequest;
      console.log(`⏱️ Sequential spacing: waiting ${Math.ceil(waitTime/1000)}s between requests`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Queue a request for sequential processing
   */
  private async queueRequest<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        resolve: async () => {
          try {
            const result = await requestFn();
            resolve(result);
          } catch (error) {
            reject(error);
          }
        },
        reject,
        timestamp: Date.now()
      });

      this.processQueue();
    });
  }

  /**
   * Process the request queue sequentially
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;
    console.log(`🔄 Processing queue: ${this.requestQueue.length} requests pending`);

    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();
      if (request) {
        try {
          await request.resolve();
        } catch (error) {
          request.reject(error);
        }
      }
    }

    this.isProcessingQueue = false;
    console.log(`✅ Queue processing complete`);
  }

  /**
   * Generate a sprite image using NANO BANANA AI image generation with 8-frame animation
   */
  async generateSprite(options: GenerationOptions): Promise<GenerationResult> {
    try {
      await this.ensureInitialized();

      const { prompt, referenceImages = [], style = 'pixel art', size = { width: 32, height: 32 }, pose = 'idle', colorPalette } = options;

      console.log(`🎨 Generating AI sprite for: "${prompt}" in ${pose} pose`);

      // Use Gemini to analyze the prompt and extract sprite characteristics
      const characteristics = await this.analyzePromptWithGemini(prompt, pose, style);

      // Generate single high-quality AI sprite, then create animation frames
      const baseSprite = await this.generateSingleAISprite(prompt, pose, style, characteristics, colorPalette);
      const animationFrames = await this.createAnimationFromBase(baseSprite, pose, characteristics);

      return {
        success: true,
        imageData: animationFrames[0], // Main sprite (first frame)
        animationFrames, // All 8 frames for animation
        enhancedPrompt: JSON.stringify(characteristics),
        frameCount: animationFrames.length
      };

    } catch (error) {
      console.error('❌ AI sprite generation error:', error);
      // Fallback to Canvas generation if AI fails
      console.log('🔄 Falling back to Canvas generation...');
      return this.generateCanvasFallback(options);
    }
  }

  /**
   * Generate single high-quality AI sprite using NANO BANANA with sequential processing
   */
  private async generateSingleAISprite(
    prompt: string,
    pose: string,
    style: string,
    characteristics: SpriteCharacteristics,
    colorPalette?: any
  ): Promise<string> {
    console.log('🎨 Generating single AI sprite with NANO BANANA (sequential)...');

    // Use sequential queue processing for AI generation
    return this.queueRequest(async () => {
      // Wait for rate limit clearance with sequential spacing
      await this.waitForRateLimit();

    const { features, bodyType } = characteristics;
    const colorInfo = colorPalette?.colors ? ` using colors ${colorPalette.colors.join(', ')}` : '';

    const aiPrompt = `Create a pixel art sprite of ${prompt} in ${pose} pose.
    Character type: ${bodyType}
    Features: ${features.join(', ')}
    Style: ${style}, 32x32 pixels, crisp pixel art, no anti-aliasing
    ${colorInfo}

    Requirements:
    - Exact 32x32 pixel dimensions
    - Clean pixel art style with sharp edges
    - Clear, recognizable character features
    - Suitable for game sprite
    - Transparent background
    - High contrast colors for visibility
    - Professional game art quality`;

    console.log(`📝 AI prompt: ${aiPrompt}`);

    const maxRetries = 3;
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 Attempt ${attempt}/${maxRetries} for AI sprite generation`);

        const result = await this.imageModel.generateContent(aiPrompt);
        const response = await result.response;

        // Record successful request
        this.recordSuccessfulRequest(aiPrompt.length);

        // Extract image data from response
        const candidates = response.candidates;
        if (candidates && candidates[0] && candidates[0].content && candidates[0].content.parts) {
          for (const part of candidates[0].content.parts) {
            if (part.inlineData && part.inlineData.data) {
              const base64Data = part.inlineData.data;
              console.log('✅ AI sprite generated successfully');
              return `data:image/png;base64,${base64Data}`;
            }
          }
        }

        throw new Error('Failed to extract image data from AI response');

      } catch (error: any) {
        lastError = error;

        // Check if this is a rate limit error
        if (error.status === 429) {
          const { shouldRetry, retryAfter } = this.handleRateLimitError(error);

          if (shouldRetry && attempt < maxRetries) {
            console.log(`⏳ Waiting ${Math.ceil(retryAfter/1000)}s before retry...`);
            await new Promise(resolve => setTimeout(resolve, retryAfter));
            continue;
          } else {
            console.log('🚫 Max retries reached or retry not recommended');
            break;
          }
        } else {
          // Non-rate-limit error, don't retry
          console.error('❌ Non-rate-limit error:', error.message);
          break;
        }
      }
    }

      console.error('❌ AI sprite generation failed after all retries:', lastError?.message);
      throw lastError;
    });
  }

  /**
   * Create 8-frame animation from base AI sprite using programmatic modifications
   */
  private async createAnimationFromBase(baseSprite: string, pose: string, characteristics: SpriteCharacteristics): Promise<string[]> {
    console.log('🎬 Creating 8-frame animation from AI base sprite...');

    try {
      // For now, create 8 copies of the base sprite
      // In the future, this could apply programmatic transformations for animation
      const frames = Array(8).fill(baseSprite);

      console.log(`🎬 ✅ Created ${frames.length} animation frames from AI base`);
      return frames;
    } catch (error) {
      console.error('❌ Animation creation failed:', error);
      // Return single frame as fallback
      return [baseSprite];
    }
  }



  /**
   * Use Gemini to analyze prompt and extract sprite characteristics
   */
  private async analyzePromptWithGemini(prompt: string, pose: string, style: string): Promise<SpriteCharacteristics> {
    // Try Gemini API regardless of fetch check - let it fail gracefully if needed
    try {
      console.log('🧠 Attempting Gemini AI analysis...');

      const analysisPrompt = `
        Analyze this character description for pixel art sprite generation: "${prompt}"

        Extract and provide ONLY the following information in this exact JSON format:
        {
          "bodyType": "human/creature/robot/etc",
          "colors": ["#color1", "#color2", "#color3"],
          "features": ["feature1", "feature2", "feature3"],
          "pose": "${pose}",
          "style": "${style}"
        }

        For colors, provide 3-5 hex color codes that would be appropriate for this character.
        For features, list 3-5 key visual characteristics (hair, clothing, weapons, etc).
        Respond with ONLY the JSON, no other text.
      `;

      const result = await this.textModel.generateContent(analysisPrompt);
      const response = await result.response;
      const analysisText = response.text();

      console.log('🧠 ✅ Gemini AI analysis successful:', analysisText);

      // Parse the JSON response
      try {
        const parsed = JSON.parse(analysisText.replace(/```json|```/g, '').trim());
        return {
          bodyType: parsed.bodyType || 'human',
          colors: parsed.colors || ['#4A90E2', '#7ED321', '#F5A623'],
          features: parsed.features || ['basic', 'character'],
          pose: pose,
          style: style
        };
      } catch (parseError) {
        console.warn('Failed to parse Gemini response, using defaults');
        return this.getDefaultCharacteristics(prompt, pose, style);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.warn('🧠 ❌ Gemini analysis failed, using intelligent defaults:', errorMessage);
      return this.getDefaultCharacteristics(prompt, pose, style);
    }
  }

  /**
   * Generate default characteristics when Gemini analysis fails
   */
  /**
   * Get current rate limit status for monitoring
   */
  public getRateLimitStatus(): any {
    const now = Date.now();
    this.resetCountersIfNeeded(now);

    return {
      requests: {
        perMinute: {
          current: this.rateLimitTracker.requestsPerMinute,
          limit: this.rateLimitConfig.maxRequestsPerMinute,
          resetIn: Math.ceil((60000 - (now - this.rateLimitTracker.lastMinuteReset)) / 1000)
        },
        perDay: {
          current: this.rateLimitTracker.requestsPerDay,
          limit: this.rateLimitConfig.maxRequestsPerDay,
          resetIn: Math.ceil((86400000 - (now - this.rateLimitTracker.lastDayReset)) / 3600000) // hours
        }
      },
      tokens: {
        perMinute: {
          current: this.rateLimitTracker.tokensPerMinute,
          limit: this.rateLimitConfig.maxTokensPerMinute
        }
      },
      errors: {
        consecutive: this.rateLimitTracker.consecutiveErrors,
        retryAfter: this.rateLimitTracker.retryAfter ? Math.ceil((this.rateLimitTracker.retryAfter - now) / 1000) : 0
      },
      canMakeRequest: this.canMakeRequest().allowed
    };
  }

  /**
   * Check if AI generation is available (not rate limited)
   */
  public isAIGenerationAvailable(): boolean {
    return this.canMakeRequest().allowed;
  }

  /**
   * Canvas fallback generation when AI fails
   */
  private async generateCanvasFallback(options: GenerationOptions): Promise<GenerationResult> {
    try {
      const { prompt, style = 'pixel art', size = { width: 32, height: 32 }, pose = 'idle', colorPalette } = options;

      console.log('🎨 Using Canvas fallback generation...');

      // Use simple keyword analysis for characteristics
      const characteristics = this.getDefaultCharacteristics(prompt, pose, style);

      // Generate single frame using Canvas
      const spriteImageData = await this.generatePixelArtSprite(characteristics, size, colorPalette);

      return {
        success: true,
        imageData: spriteImageData,
        enhancedPrompt: JSON.stringify(characteristics),
        frameCount: 1
      };
    } catch (error) {
      console.error('❌ Canvas fallback failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate sprite with fallback'
      };
    }
  }

  private getDefaultCharacteristics(prompt: string, pose: string, style: string): SpriteCharacteristics {
    // Simple keyword-based analysis as fallback
    const lowerPrompt = prompt.toLowerCase();

    let bodyType = 'human';
    if (lowerPrompt.includes('robot') || lowerPrompt.includes('mech')) bodyType = 'robot';
    else if (lowerPrompt.includes('dragon') || lowerPrompt.includes('beast')) bodyType = 'creature';

    let colors = ['#4A90E2', '#7ED321', '#F5A623']; // Default blue, green, orange
    if (lowerPrompt.includes('red')) colors = ['#DC143C', '#8B0000', '#FFB6C1'];
    else if (lowerPrompt.includes('blue')) colors = ['#4169E1', '#191970', '#87CEEB'];
    else if (lowerPrompt.includes('green')) colors = ['#228B22', '#006400', '#90EE90'];
    else if (lowerPrompt.includes('dark')) colors = ['#2F2F2F', '#696969', '#A9A9A9'];

    const features = [];
    if (lowerPrompt.includes('hair')) features.push('hair');
    if (lowerPrompt.includes('armor') || lowerPrompt.includes('knight')) features.push('armor');
    if (lowerPrompt.includes('sword') || lowerPrompt.includes('weapon')) features.push('weapon');
    if (lowerPrompt.includes('robe') || lowerPrompt.includes('cloak')) features.push('robe');
    if (features.length === 0) features.push('basic');

    return { bodyType, colors, features, pose, style };
  }

  /**
   * Generate actual pixel art sprite using Canvas
   */
  private async generatePixelArtSprite(
    characteristics: SpriteCharacteristics,
    size: { width: number; height: number },
    colorPalette?: any
  ): Promise<string> {
    const canvas = createCanvas(size.width, size.height);
    const ctx = canvas.getContext('2d');

    // Clear canvas with transparent background
    ctx.clearRect(0, 0, size.width, size.height);

    // Use provided color palette or characteristics colors
    const colors = colorPalette?.colors || characteristics.colors;

    // Generate sprite based on characteristics
    this.drawSpriteBody(ctx, characteristics, colors, size);
    this.drawSpriteFeatures(ctx, characteristics, colors, size);
    this.applyPoseModifications(ctx, characteristics.pose, size);

    // Convert to base64 data URL
    return canvas.toDataURL('image/png');
  }

  /**
   * Generate multiple sprites for different poses (sequential processing)
   */
  async generateMultipleSprites(
    basePrompt: string,
    poses: string[],
    referenceImages?: string[],
    colorPalette?: any
  ): Promise<{ pose: string; result: GenerationResult }[]> {
    const results = [];

    console.log(`🔄 Starting sequential generation for ${poses.length} poses`);

    for (let i = 0; i < poses.length; i++) {
      const pose = poses[i];
      console.log(`🎭 Generating sprite ${i + 1}/${poses.length} for pose: ${pose}`);

      const result = await this.generateSprite({
        prompt: basePrompt,
        referenceImages,
        style: 'pixel art',
        pose: pose,
        colorPalette: colorPalette
      });

      results.push({ pose, result });

      // Log progress
      console.log(`✅ Completed ${i + 1}/${poses.length} sprites`);

      // Sequential spacing is handled by the queue system in generateSingleAISprite
      // No additional delay needed here as the queue manages timing
    }

    console.log(`🎉 Sequential generation complete: ${results.length} sprites generated`);
    return results;
  }

  /**
   * Draw the basic body structure of the sprite with detailed pixel art
   */
  private drawSpriteBody(ctx: any, characteristics: SpriteCharacteristics, colors: string[], size: { width: number; height: number }) {
    const { bodyType, pose } = characteristics;
    const skinColor = this.getSkinColor(characteristics, colors);
    const bodyColor = colors[1] || '#7ED321';

    console.log(`🎨 Drawing ${bodyType} body with colors:`, colors);

    // Set pixel-perfect rendering
    ctx.imageSmoothingEnabled = false;

    if (bodyType === 'human' || bodyType === 'humanoid') {
      this.drawHumanoidBody(ctx, skinColor, bodyColor, size, pose);
    } else if (bodyType === 'creature') {
      this.drawCreatureBody(ctx, colors, size, pose);
    } else if (bodyType === 'robot') {
      this.drawRobotBody(ctx, colors, size, pose);
    } else {
      // Default to humanoid
      this.drawHumanoidBody(ctx, skinColor, bodyColor, size, pose);
    }
  }

  /**
   * Draw detailed humanoid body with pixel art precision
   */
  private drawHumanoidBody(ctx: any, skinColor: string, bodyColor: string, size: { width: number; height: number }, pose: string) {
    const w = size.width;
    const h = size.height;

    // Head (detailed with pixel precision)
    ctx.fillStyle = skinColor;
    this.drawPixelRect(ctx, w * 0.375, h * 0.125, w * 0.25, h * 0.25);

    // Add facial features
    ctx.fillStyle = '#000000';
    // Eyes
    this.drawPixelRect(ctx, w * 0.4, h * 0.2, w * 0.05, h * 0.05);
    this.drawPixelRect(ctx, w * 0.55, h * 0.2, w * 0.05, h * 0.05);

    // Body/torso
    ctx.fillStyle = bodyColor;
    this.drawPixelRect(ctx, w * 0.3125, h * 0.375, w * 0.375, h * 0.375);

    // Arms (pose-dependent positioning)
    const armOffset = pose === 'attacking' ? -0.05 : 0;
    this.drawPixelRect(ctx, w * (0.1875 + armOffset), h * 0.4375, w * 0.125, h * 0.25);
    this.drawPixelRect(ctx, w * (0.6875 - armOffset), h * 0.4375, w * 0.125, h * 0.25);

    // Hands
    ctx.fillStyle = skinColor;
    this.drawPixelRect(ctx, w * (0.1875 + armOffset), h * 0.6875, w * 0.125, h * 0.0625);
    this.drawPixelRect(ctx, w * (0.6875 - armOffset), h * 0.6875, w * 0.125, h * 0.0625);

    // Legs (pose-dependent positioning)
    ctx.fillStyle = bodyColor;
    const legOffset = pose === 'walking' ? 0.03 : 0;
    this.drawPixelRect(ctx, w * 0.3125, h * (0.75 + legOffset), w * 0.125, h * 0.25);
    this.drawPixelRect(ctx, w * 0.5625, h * (0.75 - legOffset), w * 0.125, h * 0.25);

    // Feet
    ctx.fillStyle = '#654321';
    this.drawPixelRect(ctx, w * 0.3125, h * (0.9375 + legOffset), w * 0.125, h * 0.0625);
    this.drawPixelRect(ctx, w * 0.5625, h * (0.9375 - legOffset), w * 0.125, h * 0.0625);
  }

  /**
   * Helper method for pixel-perfect rectangle drawing
   */
  private drawPixelRect(ctx: any, x: number, y: number, width: number, height: number) {
    ctx.fillRect(Math.floor(x), Math.floor(y), Math.ceil(width), Math.ceil(height));
  }

  /**
   * Get appropriate skin color based on character analysis
   */
  private getSkinColor(characteristics: SpriteCharacteristics, colors: string[]): string {
    // Check if character has specific skin tone mentioned
    const features = characteristics.features.join(' ').toLowerCase();
    if (features.includes('pale') || features.includes('icy')) return '#F5DEB3';
    if (features.includes('dark') || features.includes('shadow')) return '#8B4513';
    if (features.includes('green') || features.includes('orc')) return '#90EE90';
    if (features.includes('blue') || features.includes('frost')) return '#ADD8E6';

    // Default human skin tone
    return '#FDBCB4';
  }

  /**
   * Draw creature body with organic shapes
   */
  private drawCreatureBody(ctx: any, colors: string[], size: { width: number; height: number }, pose: string) {
    const w = size.width;
    const h = size.height;
    const primaryColor = colors[0] || '#4A90E2';
    const secondaryColor = colors[1] || '#7ED321';

    ctx.fillStyle = primaryColor;

    // Main body (organic oval shape)
    ctx.beginPath();
    ctx.ellipse(w * 0.5, h * 0.5, w * 0.3, h * 0.4, 0, 0, 2 * Math.PI);
    ctx.fill();

    // Add creature features
    ctx.fillStyle = secondaryColor;
    // Eyes
    this.drawPixelRect(ctx, w * 0.4, h * 0.3, w * 0.05, h * 0.05);
    this.drawPixelRect(ctx, w * 0.55, h * 0.3, w * 0.05, h * 0.05);

    // Limbs/appendages
    this.drawPixelRect(ctx, w * 0.2, h * 0.6, w * 0.1, h * 0.2);
    this.drawPixelRect(ctx, w * 0.7, h * 0.6, w * 0.1, h * 0.2);
  }

  /**
   * Draw robot body with angular shapes
   */
  private drawRobotBody(ctx: any, colors: string[], size: { width: number; height: number }, pose: string) {
    const w = size.width;
    const h = size.height;
    const metalColor = colors[0] || '#C0C0C0';
    const accentColor = colors[1] || '#FF0000';

    ctx.fillStyle = metalColor;

    // Main body (rectangular)
    this.drawPixelRect(ctx, w * 0.25, h * 0.25, w * 0.5, h * 0.5);

    // Head
    this.drawPixelRect(ctx, w * 0.375, h * 0.125, w * 0.25, h * 0.125);

    // Arms
    this.drawPixelRect(ctx, w * 0.125, h * 0.375, w * 0.125, h * 0.25);
    this.drawPixelRect(ctx, w * 0.75, h * 0.375, w * 0.125, h * 0.25);

    // Legs
    this.drawPixelRect(ctx, w * 0.3125, h * 0.75, w * 0.125, h * 0.25);
    this.drawPixelRect(ctx, w * 0.5625, h * 0.75, w * 0.125, h * 0.25);

    // Robot details
    ctx.fillStyle = accentColor;
    // Eyes/sensors
    this.drawPixelRect(ctx, w * 0.4, h * 0.15, w * 0.05, h * 0.05);
    this.drawPixelRect(ctx, w * 0.55, h * 0.15, w * 0.05, h * 0.05);

    // Chest panel
    this.drawPixelRect(ctx, w * 0.4375, h * 0.4375, w * 0.125, h * 0.125);
  }

  /**
   * Draw sprite features based on AI-analyzed characteristics
   */
  private drawSpriteFeatures(ctx: any, characteristics: SpriteCharacteristics, colors: string[], size: { width: number; height: number }) {
    const { features, pose } = characteristics;
    const w = size.width;
    const h = size.height;

    console.log(`🎨 Drawing features:`, features);

    features.forEach(feature => {
      const featureLower = feature.toLowerCase();

      // Hair and head features
      if (featureLower.includes('hair') || featureLower.includes('hood') || featureLower.includes('hat')) {
        this.drawHeadwear(ctx, featureLower, colors, w, h);
      }

      // Facial features
      if (featureLower.includes('mask') || featureLower.includes('ninja mask')) {
        this.drawMask(ctx, colors, w, h);
      }

      // Clothing and armor
      if (featureLower.includes('robe') || featureLower.includes('mage robe')) {
        this.drawRobes(ctx, colors, w, h);
      }

      if (featureLower.includes('armor') || featureLower.includes('chest armor')) {
        this.drawArmor(ctx, colors, w, h);
      }

      if (featureLower.includes('ninja') || featureLower.includes('stealth') || featureLower.includes('shinobi')) {
        this.drawNinjaOutfit(ctx, colors, w, h);
      }

      // Weapons and tools
      if (featureLower.includes('staff') || featureLower.includes('crystal staff')) {
        this.drawStaff(ctx, colors, w, h, pose);
      }

      if (featureLower.includes('sword') || featureLower.includes('katana')) {
        this.drawSword(ctx, colors, w, h, pose);
      }

      if (featureLower.includes('weapon') || featureLower.includes('concealed weapon')) {
        this.drawWeapon(ctx, colors, w, h, pose);
      }

      // Special effects
      if (featureLower.includes('icy') || featureLower.includes('frost') || featureLower.includes('crystal')) {
        this.drawIcyEffects(ctx, colors, w, h);
      }
    });
  }

  /**
   * Draw headwear (hair, hats, hoods)
   */
  private drawHeadwear(ctx: any, feature: string, colors: string[], w: number, h: number) {
    const hairColor = colors[2] || '#8B4513';
    ctx.fillStyle = hairColor;

    if (feature.includes('hood') || feature.includes('pointed hat')) {
      // Pointed hood/hat
      this.drawPixelRect(ctx, w * 0.3125, h * 0.0625, w * 0.375, h * 0.125);
      this.drawPixelRect(ctx, w * 0.4375, h * 0.03125, w * 0.125, h * 0.0625);
    } else {
      // Regular hair
      this.drawPixelRect(ctx, w * 0.3125, h * 0.0625, w * 0.375, h * 0.125);
    }
  }

  /**
   * Draw ninja mask
   */
  private drawMask(ctx: any, colors: string[], w: number, h: number) {
    const maskColor = colors[0] || '#2F2F2F';
    ctx.fillStyle = maskColor;

    // Mask covering lower face
    this.drawPixelRect(ctx, w * 0.375, h * 0.25, w * 0.25, h * 0.125);

    // Leave eye area visible
    ctx.fillStyle = '#FDBCB4';
    this.drawPixelRect(ctx, w * 0.4, h * 0.1875, w * 0.2, h * 0.0625);
  }

  /**
   * Draw mage robes
   */
  private drawRobes(ctx: any, colors: string[], w: number, h: number) {
    const robeColor = colors[1] || '#4B0082';
    ctx.fillStyle = robeColor;

    // Flowing robe over body
    this.drawPixelRect(ctx, w * 0.25, h * 0.5, w * 0.5, h * 0.4375);

    // Robe sleeves
    this.drawPixelRect(ctx, w * 0.125, h * 0.4375, w * 0.1875, h * 0.25);
    this.drawPixelRect(ctx, w * 0.6875, h * 0.4375, w * 0.1875, h * 0.25);
  }

  /**
   * Draw armor
   */
  private drawArmor(ctx: any, colors: string[], w: number, h: number) {
    const armorColor = colors[3] || '#C0C0C0';
    ctx.fillStyle = armorColor;

    // Chest plate
    this.drawPixelRect(ctx, w * 0.3125, h * 0.4375, w * 0.375, h * 0.1875);

    // Shoulder guards
    this.drawPixelRect(ctx, w * 0.25, h * 0.375, w * 0.125, h * 0.125);
    this.drawPixelRect(ctx, w * 0.625, h * 0.375, w * 0.125, h * 0.125);
  }

  /**
   * Draw ninja outfit
   */
  private drawNinjaOutfit(ctx: any, colors: string[], w: number, h: number) {
    const ninjaColor = colors[0] || '#2F2F2F';
    ctx.fillStyle = ninjaColor;

    // Dark ninja gi (overwrites body color)
    this.drawPixelRect(ctx, w * 0.3125, h * 0.375, w * 0.375, h * 0.375);

    // Ninja belt
    ctx.fillStyle = colors[1] || '#8B0000';
    this.drawPixelRect(ctx, w * 0.3125, h * 0.5625, w * 0.375, h * 0.0625);
  }

  /**
   * Draw crystal staff
   */
  private drawStaff(ctx: any, colors: string[], w: number, h: number, pose: string) {
    const staffColor = colors[3] || '#8B4513';
    const crystalColor = colors[2] || '#87CEEB';

    ctx.fillStyle = staffColor;

    if (pose === 'attacking' || pose === 'casting') {
      // Staff held up
      this.drawPixelRect(ctx, w * 0.125, h * 0.125, w * 0.0625, h * 0.375);
      // Crystal top
      ctx.fillStyle = crystalColor;
      this.drawPixelRect(ctx, w * 0.125, h * 0.0625, w * 0.0625, h * 0.0625);
    } else {
      // Staff at side
      this.drawPixelRect(ctx, w * 0.75, h * 0.25, w * 0.0625, h * 0.5);
      // Crystal top
      ctx.fillStyle = crystalColor;
      this.drawPixelRect(ctx, w * 0.75, h * 0.1875, w * 0.0625, h * 0.0625);
    }
  }

  /**
   * Draw sword/katana
   */
  private drawSword(ctx: any, colors: string[], w: number, h: number, pose: string) {
    const bladeColor = '#C0C0C0';
    const handleColor = colors[3] || '#8B4513';

    if (pose === 'attacking') {
      // Sword raised
      ctx.fillStyle = handleColor;
      this.drawPixelRect(ctx, w * 0.0625, h * 0.25, w * 0.0625, h * 0.125);
      ctx.fillStyle = bladeColor;
      this.drawPixelRect(ctx, w * 0.0625, h * 0.0625, w * 0.0625, h * 0.1875);
    } else {
      // Sword sheathed at side
      ctx.fillStyle = handleColor;
      this.drawPixelRect(ctx, w * 0.75, h * 0.5, w * 0.0625, h * 0.25);
    }
  }

  /**
   * Draw generic weapon
   */
  private drawWeapon(ctx: any, colors: string[], w: number, h: number, pose: string) {
    const weaponColor = colors[3] || '#654321';
    ctx.fillStyle = weaponColor;

    if (pose === 'attacking') {
      // Weapon in hand
      this.drawPixelRect(ctx, w * 0.0625, h * 0.1875, w * 0.0625, h * 0.1875);
    }
  }

  /**
   * Draw icy/frost effects
   */
  private drawIcyEffects(ctx: any, colors: string[], w: number, h: number) {
    const iceColor = '#87CEEB';
    ctx.fillStyle = iceColor;

    // Ice crystals around character
    this.drawPixelRect(ctx, w * 0.125, h * 0.125, w * 0.0625, h * 0.0625);
    this.drawPixelRect(ctx, w * 0.8125, h * 0.1875, w * 0.0625, h * 0.0625);
    this.drawPixelRect(ctx, w * 0.0625, h * 0.8125, w * 0.0625, h * 0.0625);
  }

  /**
   * Apply pose-specific modifications
   */
  private applyPoseModifications(ctx: any, pose: string, size: { width: number; height: number }) {
    // Add pose-specific effects or modifications
    switch (pose) {
      case 'casting':
        // Add magical effect
        ctx.fillStyle = '#FF00FF';
        ctx.fillRect(size.width * 0.75, size.height * 0.25, size.width * 0.125, size.width * 0.125);
        break;

      case 'hurt':
        // Add damage effect (red overlay)
        ctx.fillStyle = 'rgba(255, 0, 0, 0.3)';
        ctx.fillRect(0, 0, size.width, size.height);
        break;

      case 'celebrating':
        // Add celebration effect
        ctx.fillStyle = '#FFD700';
        for (let i = 0; i < 3; i++) {
          ctx.fillRect(size.width * (0.2 + i * 0.3), size.height * 0.1, size.width * 0.0625, size.width * 0.0625);
        }
        break;
    }
  }

  /**
   * Prepare reference images for multimodal input
   */
  private async prepareReferenceImages(imageDataArray: string[]): Promise<any[]> {
    const imageParts = [];

    for (const imageData of imageDataArray) {
      try {
        // Convert base64 to the format expected by Gemini
        const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
        
        imageParts.push({
          inlineData: {
            data: base64Data,
            mimeType: 'image/png'
          }
        });
      } catch (error) {
        console.error('Error preparing reference image:', error);
      }
    }

    return imageParts;
  }

  /**
   * Validate API key and connection
   */
  async validateConnection(): Promise<boolean> {
    await this.ensureInitialized();

    if (!this.fetchAvailable) {
      console.log('⚠️ Fetch API not available - Gemini validation skipped');
      return false;
    }

    try {
      const result = await this.textModel.generateContent('Test connection');
      console.log('✅ Gemini API connection validated successfully');
      return true;
    } catch (error) {
      console.error('❌ Gemini connection validation failed:', error);
      return false;
    }
  }

  /**
   * Get model information
   */
  getModelInfo(): { model: string; capabilities: string[] } {
    return {
      model: 'gemini-2.5-flash + Canvas Sprite Generation',
      capabilities: [
        'AI-powered character analysis',
        'Pixel art sprite generation',
        'Multiple pose support',
        'Color palette integration',
        'Pose-specific modifications',
        'Character consistency'
      ]
    };
  }

  /**
   * Utility function to add delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default GeminiService;
