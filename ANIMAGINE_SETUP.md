# Animagine XL 4.0 Setup Guide

This guide will help you set up Animagine XL 4.0 for local AI sprite generation in SpriteGen.

## Prerequisites

### Hardware Requirements
- **GPU**: NVIDIA RTX 4060 or better (12GB+ VRAM recommended)
- **RAM**: 32GB+ system memory (for optimal performance)
- **Storage**: 15GB+ free space for model files
- **CPU**: Modern multi-core processor (Intel i7/AMD Ryzen 7+)

### Software Requirements
- Python 3.8 or higher
- CUDA 12.1 or compatible
- Git (for cloning repositories)
- Node.js 18+ (for the main application)

## Step-by-Step Setup

### 1. Check Your System

```bash
# Check Python version
python3 --version

# Check CUDA availability
nvidia-smi

# Check available disk space
df -h
```

### 2. Run the Setup Script

```bash
# Make the setup script executable
chmod +x ai-models/setup.sh

# Run the setup script
./ai-models/setup.sh
```

This script will:
- Create a Python virtual environment
- Install PyTorch with CUDA support
- Install all required dependencies
- Set up environment variables

### 3. Login to Hugging Face

You need a Hugging Face account to download the model:

```bash
# Activate the virtual environment
source ai-models/animagine-xl-env/bin/activate

# Login to Hugging Face
huggingface-cli login
```

Enter your Hugging Face token when prompted. You can get a token from: https://huggingface.co/settings/tokens

### 4. Download the Model

```bash
# Download Animagine XL 4.0 model
python3 ai-models/download_model.py --login
```

This will download approximately 6-8GB of model files.

### 5. Test the Installation

```bash
# Test the Python interface
python3 python-bridge/test_animagine.py

# Test the full integration (requires server to be running)
node test_animagine_integration.js
```

## Environment Variables

The setup creates these environment variables in `.env.animagine`:

```bash
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
XFORMERS_FORCE_DISABLE_TRITON=1
HF_HOME=./ai-models/cache
CUDA_VISIBLE_DEVICES=0
```

## Troubleshooting

### Common Issues

#### 1. CUDA Out of Memory
If you get CUDA out of memory errors:

```bash
# Reduce memory usage
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256

# Or use CPU (very slow)
export CUDA_VISIBLE_DEVICES=""
```

#### 2. Model Download Fails
```bash
# Clear cache and retry
rm -rf ai-models/cache
python3 ai-models/download_model.py --force --login
```

#### 3. Permission Errors
```bash
# Fix permissions
chmod -R 755 ai-models/
chmod +x python-bridge/animagine_interface.py
```

#### 4. Python Dependencies
```bash
# Reinstall dependencies
source ai-models/animagine-xl-env/bin/activate
pip install --upgrade -r ai-models/animagine-xl-4.0/requirements.txt
```

### Performance Optimization

#### For 8-12GB VRAM Systems
```bash
# Enable memory efficient attention
export XFORMERS_FORCE_DISABLE_TRITON=1
pip install xformers

# Use attention slicing
# (automatically enabled in the interface)
```

#### For 16GB+ VRAM Systems
```bash
# Disable memory optimizations for better speed
# Edit python-bridge/animagine_interface.py
# Comment out: pipeline.enable_attention_slicing()
# Comment out: pipeline.enable_vae_slicing()
```

## Usage

Once set up, you can use Animagine XL 4.0 through the SpriteGen API:

```javascript
// Single sprite generation
const response = await fetch('/api/generation/sprite', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: 'anime knight warrior',
    pose: 'idle',
    style: 'anime',
    aiModel: 'ANIMAGINE_XL',
    direction: 'south'
  })
});

// Directional sprite generation
const response = await fetch('/api/generation/sprite/directional', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: 'anime mage wizard',
    directions: ['north', 'south', 'east', 'west'],
    action: 'idle',
    aiModel: 'ANIMAGINE_XL'
  })
});
```

## Model Information

- **Model**: Animagine XL 4.0 by Cagliostro Lab
- **Type**: Stable Diffusion XL fine-tuned for anime/manga style
- **Resolution**: Up to 1024x1024 (recommended: 512x512 for sprites)
- **Inference Steps**: 20-30 (default: 28)
- **Guidance Scale**: 10-15 (default: 12.0)

## Next Steps

1. Start the SpriteGen server: `npm run dev`
2. Test the integration: `node test_animagine_integration.js`
3. Use the web interface to generate sprites with both models
4. Experiment with different prompts and styles

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review the console output for error messages
3. Ensure your hardware meets the requirements
4. Check the Hugging Face model page for updates

For more information, see the main IMPROVEMENTS.md file.
