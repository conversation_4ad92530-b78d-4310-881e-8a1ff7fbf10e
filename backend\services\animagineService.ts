import { spawn, ChildProcess } from 'child_process';
import path from 'path';

interface AnimagineGenerationOptions {
  prompt: string;
  pose?: string;
  style?: string;
  direction?: string;
  width?: number;
  height?: number;
  target_size?: number;
  num_inference_steps?: number;
  guidance_scale?: number;
}

interface AnimagineGenerationResult {
  success: boolean;
  imageData?: string;
  error?: string;
  metadata?: {
    model: string;
    prompt: string;
    negative_prompt: string;
    timestamp: number;
    resolution: string;
    inference_steps: number;
    guidance_scale: number;
  };
}

interface QueuedRequest {
  resolve: (result: AnimagineGenerationResult) => void;
  reject: (error: Error) => void;
  options: AnimagineGenerationOptions;
  timestamp: number;
}

class AnimagineXLService {
  private pythonProcess: ChildProcess | null = null;
  private modelLoaded: boolean = false;
  private requestQueue: QueuedRequest[] = [];
  private isProcessingQueue: boolean = false;
  private lastRequestTime: number = 0;
  private readonly baseDelayMs: number = 1000; // 1 second between requests
  
  constructor() {
    this.initializeModel();
  }

  async initializeModel(): Promise<void> {
    if (this.pythonProcess) {
      return; // Already initialized
    }

    try {
      console.log('🎨 Initializing Animagine XL 4.0 model...');
      
      // Start Python process with Animagine XL 4.0 model
      this.pythonProcess = spawn('python3', [
        path.join(process.cwd(), 'python-bridge/animagine_interface.py')
      ], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: process.cwd(),
        env: {
          ...process.env,
          CUDA_VISIBLE_DEVICES: '0',
          HF_HOME: './ai-models/cache',
          PYTORCH_CUDA_ALLOC_CONF: 'max_split_size_mb:512',
          XFORMERS_FORCE_DISABLE_TRITON: '1'
        }
      });

      // Handle process events
      this.pythonProcess.on('error', (error) => {
        console.error('❌ Animagine XL process error:', error);
        this.modelLoaded = false;
      });

      this.pythonProcess.on('exit', (code) => {
        console.log(`🔄 Animagine XL process exited with code ${code}`);
        this.modelLoaded = false;
        this.pythonProcess = null;
      });

      // Listen for stderr (debug output)
      this.pythonProcess.stderr?.on('data', (data) => {
        const message = data.toString();
        console.log('🔍 Animagine XL debug:', message.trim());
      });

      // Wait for model to be ready
      await this.waitForModelReady();
      this.modelLoaded = true;
      console.log('✅ Animagine XL 4.0 model initialized successfully');
      
    } catch (error) {
      console.error('❌ Failed to initialize Animagine XL model:', error);
      this.modelLoaded = false;
      throw error;
    }
  }

  private async waitForModelReady(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.pythonProcess || !this.pythonProcess.stdout) {
        reject(new Error('Python process not available'));
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('Model initialization timeout (5 minutes)'));
      }, 5 * 60 * 1000); // 5 minute timeout

      this.pythonProcess.stdout.on('data', (data) => {
        const message = data.toString().trim();
        if (message.includes('ANIMAGINE_XL_READY')) {
          clearTimeout(timeout);
          resolve();
        }
      });
    });
  }

  async generateSprite(options: AnimagineGenerationOptions): Promise<AnimagineGenerationResult> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        resolve,
        reject,
        options,
        timestamp: Date.now()
      });
      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift()!;
      
      try {
        // Ensure minimum delay between requests
        const timeSinceLastRequest = Date.now() - this.lastRequestTime;
        if (timeSinceLastRequest < this.baseDelayMs) {
          await new Promise(resolve => setTimeout(resolve, this.baseDelayMs - timeSinceLastRequest));
        }

        const result = await this.executeGeneration(request.options);
        this.lastRequestTime = Date.now();
        request.resolve(result);
        
      } catch (error) {
        request.reject(error as Error);
      }
    }

    this.isProcessingQueue = false;
  }

  private async executeGeneration(options: AnimagineGenerationOptions): Promise<AnimagineGenerationResult> {
    if (!this.modelLoaded || !this.pythonProcess) {
      await this.initializeModel();
    }

    return new Promise((resolve, reject) => {
      if (!this.pythonProcess || !this.pythonProcess.stdin || !this.pythonProcess.stdout) {
        reject(new Error('Python process not available'));
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('Generation timeout (2 minutes)'));
      }, 2 * 60 * 1000); // 2 minute timeout

      // Listen for response
      const responseHandler = (data: Buffer) => {
        try {
          const lines = data.toString().split('\n');
          for (const line of lines) {
            if (line.trim() && line.startsWith('{')) {
              const result = JSON.parse(line.trim()) as AnimagineGenerationResult;
              clearTimeout(timeout);
              this.pythonProcess?.stdout?.removeListener('data', responseHandler);
              resolve(result);
              return;
            }
          }
        } catch (error) {
          // Continue listening for valid JSON
        }
      };

      this.pythonProcess.stdout.on('data', responseHandler);

      // Send generation request
      const request = JSON.stringify(options);
      this.pythonProcess.stdin.write(request + '\n');
    });
  }

  async isAvailable(): Promise<boolean> {
    return this.modelLoaded && this.pythonProcess !== null;
  }

  async getModelInfo(): Promise<any> {
    return {
      name: 'Animagine XL 4.0 (Local)',
      type: 'local',
      status: this.modelLoaded ? 'ready' : 'loading',
      capabilities: {
        maxResolution: { width: 1024, height: 1024 },
        supportedFormats: ['PNG', 'JPG'],
        directionalSupport: true,
        pixelArtOptimized: true,
        animationFrames: 12,
        animeStyleOptimized: true,
        characterFocused: true
      }
    };
  }

  async shutdown(): Promise<void> {
    if (this.pythonProcess) {
      this.pythonProcess.kill();
      this.pythonProcess = null;
    }
    this.modelLoaded = false;
  }
}

export default AnimagineXLService;
