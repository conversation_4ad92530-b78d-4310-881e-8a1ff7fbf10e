{"name": "spritegen", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "backend": "npx ts-node backend/server.ts", "backend:dev": "npx ts-node --watch backend/server.ts", "dev:full": "concurrently \"npm run dev\" \"npm run backend:dev\""}, "dependencies": {"@google/generative-ai": "^0.24.1", "axios": "^1.11.0", "canvas": "^3.2.0", "cors": "^2.8.5", "cross-fetch": "^4.1.0", "dotenv": "^17.2.2", "express": "^4.18.2", "gifenc": "^1.0.3", "multer": "^2.0.2", "next": "^12.3.4", "react": "^18.2.0", "react-dom": "^18.2.0", "upng-js": "^2.1.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/multer": "^2.0.0", "@types/node": "24.3.1", "@types/react": "19.1.12", "@types/react-dom": "19.1.9", "autoprefixer": "^10.4.14", "concurrently": "^9.2.1", "eslint": "9.35.0", "eslint-config-next": "15.5.2", "postcss": "^8.4.14", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "typescript": "5.9.2"}}