import React, { useState, useEffect } from 'react';
import { WorkflowStage } from '../types';
import { useSprite } from '../hooks/useSprite';
import { useImageProcessing } from '../hooks/useImageProcessing';
import WorkflowNavigation from './WorkflowNavigation';
import ConceptionStage from './stages/ConceptionStage';
import GenerationStage from './stages/GenerationStage';
import ExportStage from './stages/ExportStage';
import ErrorBoundary from './ErrorBoundary';
import LoadingOverlay from './LoadingOverlay';
import { healthCheck } from '../utils/apiClient';

const SpriteGenApp: React.FC = () => {
  const [apiStatus, setApiStatus] = useState<'checking' | 'connected' | 'error'>('checking');
  const [apiError, setApiError] = useState<string | null>(null);

  const spriteHook = useSprite();
  const imageProcessingHook = useImageProcessing();

  const {
    workflowState,
    setWorkflowStage,
    isGenerating,
    error: spriteError,
    clearError: clearSpriteError,
    loadAvailablePoses
  } = spriteHook;

  const {
    isProcessing,
    error: processingError,
    clearError: clearProcessingError
  } = imageProcessingHook;

  // Check API status on mount
  useEffect(() => {
    const checkApiStatus = async () => {
      try {
        await healthCheck();
        setApiStatus('connected');
        setApiError(null);
        
        // Load available poses
        await loadAvailablePoses();
      } catch (error: any) {
        setApiStatus('error');
        setApiError(error.message);
      }
    };

    checkApiStatus();
  }, [loadAvailablePoses]);

  // Handle stage navigation
  const handleStageChange = (stage: WorkflowStage) => {
    setWorkflowStage(stage);
    clearSpriteError();
    clearProcessingError();
  };

  // Render current stage
  const renderCurrentStage = () => {
    switch (workflowState.stage) {
      case 'conception':
        return (
          <ConceptionStage
            spriteHook={spriteHook}
            imageProcessingHook={imageProcessingHook}
            onNext={() => handleStageChange('generation')}
          />
        );
      case 'generation':
        return (
          <GenerationStage
            spriteHook={spriteHook}
            imageProcessingHook={imageProcessingHook}
            onNext={() => handleStageChange('export')}
            onBack={() => handleStageChange('conception')}
          />
        );
      case 'export':
        return (
          <ExportStage
            spriteHook={spriteHook}
            imageProcessingHook={imageProcessingHook}
            onBack={() => handleStageChange('generation')}
            onRestart={() => handleStageChange('conception')}
          />
        );
      default:
        return null;
    }
  };

  // Show API connection error
  if (apiStatus === 'error') {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center p-8">
          <h1 className="text-4xl font-bold mb-4 text-red-400">Connection Error</h1>
          <p className="text-xl mb-6">Unable to connect to the SpriteGen API</p>
          <p className="text-gray-400 mb-8">{apiError}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg font-semibold transition-colors"
          >
            Retry Connection
          </button>
        </div>
      </div>
    );
  }

  // Show loading while checking API
  if (apiStatus === 'checking') {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <p className="text-xl">Connecting to SpriteGen...</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-900 text-white">
        {/* Header */}
        <header className="bg-gray-800 border-b border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <h1 className="text-2xl font-bold text-blue-400">SpriteGen</h1>
                <span className="ml-2 text-sm text-gray-400">AI-Powered Sprite Generator</span>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-sm text-gray-400">API Connected</span>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Workflow Navigation */}
        <WorkflowNavigation
          currentStage={workflowState.stage}
          onStageChange={handleStageChange}
          canNavigate={{
            conception: true,
            generation: !!spriteHook.currentCharacter,
            export: workflowState.generatedSprites.length > 0
          }}
        />

        {/* Error Display */}
        {(spriteError || processingError) && (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="bg-red-900 border border-red-700 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <span className="text-red-200">{spriteError || processingError}</span>
                </div>
                <button
                  onClick={() => {
                    clearSpriteError();
                    clearProcessingError();
                  }}
                  className="text-red-400 hover:text-red-300"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {renderCurrentStage()}
        </main>

        {/* Loading Overlay */}
        {(isGenerating || isProcessing) && (
          <LoadingOverlay
            message={isGenerating ? 'Generating sprites...' : 'Processing images...'}
          />
        )}

        {/* Footer */}
        <footer className="bg-gray-800 border-t border-gray-700 mt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center text-gray-400">
              <p>&copy; 2024 SpriteGen. Powered by AI and creativity.</p>
            </div>
          </div>
        </footer>
      </div>
    </ErrorBoundary>
  );
};

export default SpriteGenApp;
