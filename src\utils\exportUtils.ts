import { Sprite, SpritesheetExport, Animation, Character } from '../types';
import { generateAnimationMetadata } from './animationUtils';

export interface ExportOptions {
  format: 'png' | 'gif' | 'apng' | 'spritesheet';
  gameEngine?: 'unity' | 'godot' | 'unreal' | 'generic';
  includeMetadata: boolean;
  compression?: 'none' | 'low' | 'medium' | 'high';
}

export interface ExportResult {
  files: ExportFile[];
  metadata?: any;
}

export interface ExportFile {
  name: string;
  data: Blob;
  type: string;
}

/**
 * Export sprites in various formats
 */
export async function exportSprites(
  sprites: Sprite[],
  options: ExportOptions,
  character?: Character,
  animation?: Animation
): Promise<ExportResult> {
  const { format, gameEngine = 'generic', includeMetadata } = options;

  switch (format) {
    case 'png':
      return exportAsPNG(sprites, options, character);
    case 'spritesheet':
      return exportAsSpritesheet(sprites, options, character, animation);
    case 'gif':
      return exportAsGIF(sprites, options, character, animation);
    case 'apng':
      return exportAsAPNG(sprites, options, character, animation);
    default:
      throw new Error(`Unsupported export format: ${format}`);
  }
}

/**
 * Export individual PNG files
 */
async function exportAsPNG(
  sprites: Sprite[],
  options: ExportOptions,
  character?: Character
): Promise<ExportResult> {
  const files: ExportFile[] = [];

  for (let i = 0; i < sprites.length; i++) {
    const sprite = sprites[i];
    const blob = await dataURLToBlob(sprite.imageData);
    
    const fileName = character 
      ? `${character.name}_${sprite.pose}_${i + 1}.png`
      : `sprite_${sprite.pose}_${i + 1}.png`;

    files.push({
      name: fileName,
      data: blob,
      type: 'image/png'
    });
  }

  let metadata;
  if (options.includeMetadata) {
    metadata = generatePNGMetadata(sprites, character);
    const metadataBlob = new Blob([JSON.stringify(metadata, null, 2)], {
      type: 'application/json'
    });
    
    files.push({
      name: 'metadata.json',
      data: metadataBlob,
      type: 'application/json'
    });
  }

  return { files, metadata };
}

/**
 * Export as spritesheet with metadata
 */
async function exportAsSpritesheet(
  sprites: Sprite[],
  options: ExportOptions,
  character?: Character,
  animation?: Animation
): Promise<ExportResult> {
  const { gameEngine, includeMetadata } = options;
  
  // Generate spritesheet (this would use the spritesheet generation from imageUtils)
  const spritesheet = await generateSpritesheetData(sprites);
  const spritesheetBlob = await dataURLToBlob(spritesheet.imageData);

  const files: ExportFile[] = [];
  const baseName = character?.name || 'spritesheet';

  files.push({
    name: `${baseName}.png`,
    data: spritesheetBlob,
    type: 'image/png'
  });

  if (includeMetadata) {
    const metadata = generateGameEngineMetadata(
      sprites,
      spritesheet,
      gameEngine!,
      character,
      animation
    );

    const metadataFileName = getMetadataFileName(gameEngine!);
    const metadataBlob = new Blob([JSON.stringify(metadata, null, 2)], {
      type: 'application/json'
    });

    files.push({
      name: `${baseName}${metadataFileName}`,
      data: metadataBlob,
      type: 'application/json'
    });
  }

  return { files };
}

/**
 * Export as GIF animation
 */
async function exportAsGIF(
  sprites: Sprite[],
  options: ExportOptions,
  character?: Character,
  animation?: Animation
): Promise<ExportResult> {
  // This would use the GIF creation from animationUtils
  const { createGifAnimation } = await import('./animationUtils');
  
  const frameRate = animation?.frameRate || 8;
  const loop = animation?.loop !== false;

  const gifBlob = await createGifAnimation(sprites, {
    frameRate,
    loop,
    quality: options.compression === 'high' ? 'high' : 'medium'
  });

  const fileName = character 
    ? `${character.name}_animation.gif`
    : 'sprite_animation.gif';

  return {
    files: [{
      name: fileName,
      data: gifBlob,
      type: 'image/gif'
    }]
  };
}

/**
 * Export as APNG animation
 */
async function exportAsAPNG(
  sprites: Sprite[],
  options: ExportOptions,
  character?: Character,
  animation?: Animation
): Promise<ExportResult> {
  // This would use the APNG creation from animationUtils
  const { createApngAnimation } = await import('./animationUtils');
  
  const frameRate = animation?.frameRate || 8;
  const loop = animation?.loop !== false;

  const apngBlob = await createApngAnimation(sprites, {
    frameRate,
    loop
  });

  const fileName = character 
    ? `${character.name}_animation.png`
    : 'sprite_animation.png';

  return {
    files: [{
      name: fileName,
      data: apngBlob,
      type: 'image/png'
    }]
  };
}

/**
 * Generate game engine specific metadata
 */
function generateGameEngineMetadata(
  sprites: Sprite[],
  spritesheet: SpritesheetExport,
  gameEngine: string,
  character?: Character,
  animation?: Animation
): any {
  const baseMetadata = {
    version: '1.0.0',
    generator: 'SpriteGen',
    character: character?.name || 'Unknown',
    spriteCount: sprites.length,
    spritesheet: spritesheet.metadata
  };

  switch (gameEngine) {
    case 'unity':
      return generateUnityMetadata(baseMetadata, sprites, spritesheet, animation);
    case 'godot':
      return generateGodotMetadata(baseMetadata, sprites, spritesheet, animation);
    case 'unreal':
      return generateUnrealMetadata(baseMetadata, sprites, spritesheet, animation);
    default:
      return generateGenericMetadata(baseMetadata, sprites, spritesheet, animation);
  }
}

/**
 * Generate Unity-compatible metadata
 */
function generateUnityMetadata(
  baseMetadata: any,
  sprites: Sprite[],
  spritesheet: SpritesheetExport,
  animation?: Animation
): any {
  return {
    ...baseMetadata,
    unity: {
      textureImporter: {
        textureType: 'Sprite',
        spriteMode: 2, // Multiple sprites
        pixelsPerUnit: 32,
        filterMode: 'Point', // For pixel art
        textureCompression: 'None'
      },
      sprites: sprites.map((sprite, index) => ({
        name: `${sprite.pose}_${index}`,
        rect: {
          x: sprite.x || 0,
          y: sprite.y || 0,
          width: sprite.width,
          height: sprite.height
        },
        pivot: { x: 0.5, y: 0.5 },
        border: { x: 0, y: 0, z: 0, w: 0 }
      })),
      animation: animation ? {
        name: animation.name,
        frameRate: animation.frameRate,
        loop: animation.loop,
        frames: sprites.map((sprite, index) => ({
          sprite: `${sprite.pose}_${index}`,
          time: index / animation.frameRate
        }))
      } : undefined
    }
  };
}

/**
 * Generate Godot-compatible metadata
 */
function generateGodotMetadata(
  baseMetadata: any,
  sprites: Sprite[],
  spritesheet: SpritesheetExport,
  animation?: Animation
): any {
  return {
    ...baseMetadata,
    godot: {
      resource_type: 'SpriteFrames',
      animations: animation ? {
        [animation.name]: {
          frames: sprites.map((sprite, index) => ({
            texture: `res://sprites/${sprite.pose}_${index}.png`,
            duration: 1.0 / animation.frameRate
          })),
          loop: animation.loop,
          speed: animation.frameRate
        }
      } : {},
      sprites: sprites.map((sprite, index) => ({
        name: `${sprite.pose}_${index}`,
        region: {
          x: sprite.x || 0,
          y: sprite.y || 0,
          w: sprite.width,
          h: sprite.height
        }
      }))
    }
  };
}

/**
 * Generate Unreal Engine metadata
 */
function generateUnrealMetadata(
  baseMetadata: any,
  sprites: Sprite[],
  spritesheet: SpritesheetExport,
  animation?: Animation
): any {
  return {
    ...baseMetadata,
    unreal: {
      textureGroup: 'UI',
      compressionSettings: 'UserInterface2D',
      filter: 'Nearest', // For pixel art
      sprites: sprites.map((sprite, index) => ({
        name: `${sprite.pose}_${index}`,
        sourceUV: {
          x: sprite.x || 0,
          y: sprite.y || 0
        },
        sourceDimension: {
          x: sprite.width,
          y: sprite.height
        },
        pivot: { x: 0.5, y: 0.5 }
      }))
    }
  };
}

/**
 * Generate generic metadata
 */
function generateGenericMetadata(
  baseMetadata: any,
  sprites: Sprite[],
  spritesheet: SpritesheetExport,
  animation?: Animation
): any {
  return {
    ...baseMetadata,
    sprites: sprites.map((sprite, index) => ({
      id: sprite.id,
      name: `${sprite.pose}_${index}`,
      pose: sprite.pose,
      x: sprite.x || 0,
      y: sprite.y || 0,
      width: sprite.width,
      height: sprite.height
    })),
    animation: animation ? {
      name: animation.name,
      frameRate: animation.frameRate,
      loop: animation.loop,
      frameCount: sprites.length
    } : undefined
  };
}

/**
 * Generate PNG metadata
 */
function generatePNGMetadata(sprites: Sprite[], character?: Character): any {
  return {
    character: character?.name || 'Unknown',
    sprites: sprites.map(sprite => ({
      id: sprite.id,
      pose: sprite.pose,
      width: sprite.width,
      height: sprite.height
    })),
    exportDate: new Date().toISOString(),
    generator: 'SpriteGen v1.0.0'
  };
}

/**
 * Get metadata file name for game engine
 */
function getMetadataFileName(gameEngine: string): string {
  switch (gameEngine) {
    case 'unity':
      return '.meta';
    case 'godot':
      return '.tres';
    case 'unreal':
      return '.uasset';
    default:
      return '_metadata.json';
  }
}

/**
 * Generate spritesheet data (placeholder - would use actual implementation)
 */
async function generateSpritesheetData(sprites: Sprite[]): Promise<SpritesheetExport> {
  // This would use the actual spritesheet generation from imageUtils
  // For now, return a placeholder
  return {
    imageData: sprites[0]?.imageData || '',
    metadata: {
      width: 128,
      height: 128,
      spriteWidth: 32,
      spriteHeight: 32,
      sprites: sprites.map((sprite, index) => ({
        id: sprite.id,
        name: sprite.pose,
        x: (index % 4) * 32,
        y: Math.floor(index / 4) * 32,
        width: 32,
        height: 32
      }))
    }
  };
}

/**
 * Convert data URL to Blob
 */
async function dataURLToBlob(dataURL: string): Promise<Blob> {
  const response = await fetch(dataURL);
  return response.blob();
}

/**
 * Download files as ZIP
 */
export async function downloadAsZip(files: ExportFile[], zipName: string): Promise<void> {
  // This would require a ZIP library like JSZip
  // For now, download files individually
  for (const file of files) {
    downloadFile(file.data, file.name);
  }
}

/**
 * Download single file
 */
export function downloadFile(blob: Blob, fileName: string): void {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}
