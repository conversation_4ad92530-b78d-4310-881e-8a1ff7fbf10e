import { useState, useCallback } from 'react';
import { 
  Sprite, 
  SpritePose, 
  ColorPalette, 
  Character,
  WorkflowState,
  WorkflowStage 
} from '../types';
import {
  generateSprite,
  generateBatchSprites,
  generateVariations,
  enhancePrompt,
  getAvailablePoses,
  SpriteGenerationRequest,
  BatchSpriteGenerationRequest,
  VariationGenerationRequest
} from '../utils/apiClient';

export interface UseSpriteReturn {
  // State
  sprites: Sprite[];
  currentCharacter: Character | null;
  workflowState: WorkflowState;
  isGenerating: boolean;
  error: string | null;
  availablePoses: SpritePose[];

  // Actions
  generateSingleSprite: (request: SpriteGenerationRequest) => Promise<Sprite | null>;
  generateMultipleSprites: (request: BatchSpriteGenerationRequest) => Promise<Sprite[]>;
  generateCharacterVariations: (request: VariationGenerationRequest) => Promise<Sprite[]>;
  enhancePromptText: (prompt: string, style?: string, pose?: SpritePose) => Promise<string>;
  loadAvailablePoses: () => Promise<void>;
  
  // Workflow management
  setWorkflowStage: (stage: WorkflowStage) => void;
  setCurrentCharacter: (character: Character) => void;
  addSprite: (sprite: Sprite) => void;
  removeSprite: (spriteId: string) => void;
  updateSprite: (spriteId: string, updates: Partial<Sprite>) => void;
  clearSprites: () => void;
  
  // Error handling
  clearError: () => void;
}

export function useSprite(): UseSpriteReturn {
  const [sprites, setSprites] = useState<Sprite[]>([]);
  const [currentCharacter, setCurrentCharacterState] = useState<Character | null>(null);
  const [workflowState, setWorkflowState] = useState<WorkflowState>({
    stage: 'conception',
    selectedPoses: [],
    generatedSprites: [],
    isGenerating: false
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [availablePoses, setAvailablePoses] = useState<SpritePose[]>([]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const generateSingleSprite = useCallback(async (request: SpriteGenerationRequest): Promise<Sprite | null> => {
    setIsGenerating(true);
    setError(null);

    try {
      const response = await generateSprite(request);
      
      if (response.success && response.imageData) {
        const newSprite: Sprite = {
          id: `sprite_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          imageData: response.imageData,
          pose: request.pose,
          width: request.size?.width || 32,
          height: request.size?.height || 32
        };

        setSprites(prev => [...prev, newSprite]);
        setWorkflowState(prev => ({
          ...prev,
          generatedSprites: [...prev.generatedSprites, newSprite]
        }));

        return newSprite;
      } else {
        throw new Error(response.error || 'Failed to generate sprite');
      }
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const generateMultipleSprites = useCallback(async (request: BatchSpriteGenerationRequest): Promise<Sprite[]> => {
    setIsGenerating(true);
    setError(null);

    try {
      const response = await generateBatchSprites(request);
      const newSprites: Sprite[] = [];

      if (response.success && response.results) {
        response.results.forEach((result: any, index: number) => {
          if (result.success && result.imageData) {
            const sprite: Sprite = {
              id: `sprite_${Date.now()}_${index}_${Math.random().toString(36).substr(2, 9)}`,
              imageData: result.imageData,
              pose: result.pose,
              width: 32,
              height: 32
            };
            newSprites.push(sprite);
          }
        });

        setSprites(prev => [...prev, ...newSprites]);
        setWorkflowState(prev => ({
          ...prev,
          generatedSprites: [...prev.generatedSprites, ...newSprites]
        }));
      }

      return newSprites;
    } catch (err: any) {
      setError(err.message);
      return [];
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const generateCharacterVariations = useCallback(async (request: VariationGenerationRequest): Promise<Sprite[]> => {
    setIsGenerating(true);
    setError(null);

    try {
      const response = await generateVariations(request);
      const newSprites: Sprite[] = [];

      if (response.success && response.results) {
        response.results.forEach((result: any, index: number) => {
          if (result.success && result.imageData) {
            const sprite: Sprite = {
              id: `variation_${Date.now()}_${index}_${Math.random().toString(36).substr(2, 9)}`,
              imageData: result.imageData,
              pose: request.pose || 'idle',
              width: 32,
              height: 32
            };
            newSprites.push(sprite);
          }
        });

        setSprites(prev => [...prev, ...newSprites]);
      }

      return newSprites;
    } catch (err: any) {
      setError(err.message);
      return [];
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const enhancePromptText = useCallback(async (
    prompt: string, 
    style?: string, 
    pose?: SpritePose
  ): Promise<string> => {
    try {
      const response = await enhancePrompt(prompt, style, pose);
      return response.enhancedPrompt || prompt;
    } catch (err: any) {
      setError(err.message);
      return prompt;
    }
  }, []);

  const loadAvailablePoses = useCallback(async () => {
    try {
      const response = await getAvailablePoses();
      if (response.success && response.poses) {
        setAvailablePoses(response.poses.map((p: any) => p.id));
      }
    } catch (err: any) {
      setError(err.message);
    }
  }, []);

  const setWorkflowStage = useCallback((stage: WorkflowStage) => {
    setWorkflowState(prev => ({ ...prev, stage }));
  }, []);

  const setCurrentCharacter = useCallback((character: Character) => {
    setCurrentCharacterState(character);
    setWorkflowState(prev => ({ ...prev, character }));
  }, []);

  const addSprite = useCallback((sprite: Sprite) => {
    setSprites(prev => [...prev, sprite]);
    setWorkflowState(prev => ({
      ...prev,
      generatedSprites: [...prev.generatedSprites, sprite]
    }));
  }, []);

  const removeSprite = useCallback((spriteId: string) => {
    setSprites(prev => prev.filter(s => s.id !== spriteId));
    setWorkflowState(prev => ({
      ...prev,
      generatedSprites: prev.generatedSprites.filter(s => s.id !== spriteId)
    }));
  }, []);

  const updateSprite = useCallback((spriteId: string, updates: Partial<Sprite>) => {
    setSprites(prev => prev.map(s => s.id === spriteId ? { ...s, ...updates } : s));
    setWorkflowState(prev => ({
      ...prev,
      generatedSprites: prev.generatedSprites.map(s => s.id === spriteId ? { ...s, ...updates } : s)
    }));
  }, []);

  const clearSprites = useCallback(() => {
    setSprites([]);
    setWorkflowState(prev => ({
      ...prev,
      generatedSprites: []
    }));
  }, []);

  return {
    // State
    sprites,
    currentCharacter,
    workflowState,
    isGenerating,
    error,
    availablePoses,

    // Actions
    generateSingleSprite,
    generateMultipleSprites,
    generateCharacterVariations,
    enhancePromptText,
    loadAvailablePoses,

    // Workflow management
    setWorkflowStage,
    setCurrentCharacter,
    addSprite,
    removeSprite,
    updateSprite,
    clearSprites,

    // Error handling
    clearError
  };
}
