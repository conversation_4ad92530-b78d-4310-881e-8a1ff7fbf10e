import axios from 'axios';
import { GenerationRequest, GenerationResponse, Sprite, SpritePose, ColorPalette } from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export interface SpriteGenerationRequest {
  prompt: string;
  pose: SpritePose;
  referenceImages?: string[];
  colorPalette?: ColorPalette;
  style?: string;
  size?: { width: number; height: number };
}

export interface BatchSpriteGenerationRequest {
  basePrompt: string;
  poses: SpritePose[];
  referenceImages?: string[];
  colorPalette?: ColorPalette;
  style?: string;
}

export interface VariationGenerationRequest {
  basePrompt: string;
  variations: string[];
  pose?: SpritePose;
  referenceImages?: string[];
  colorPalette?: ColorPalette;
}

export interface ImageProcessingRequest {
  imageData: string;
  removeBackground?: boolean;
  backgroundColor?: string;
  tolerance?: number;
  resize?: { width: number; height: number };
  pixelate?: boolean;
}

export interface SpritesheetGenerationRequest {
  sprites: Sprite[];
  columns?: number;
  padding?: number;
  backgroundColor?: string;
}

/**
 * Generate a single sprite
 */
export async function generateSprite(request: SpriteGenerationRequest): Promise<GenerationResponse> {
  try {
    const response = await apiClient.post('/generation/sprite', request);
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to generate sprite'
    };
  }
}

/**
 * Generate multiple sprites for different poses
 */
export async function generateBatchSprites(request: BatchSpriteGenerationRequest) {
  try {
    const response = await apiClient.post('/generation/sprites/batch', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || 'Failed to generate batch sprites');
  }
}

/**
 * Generate character variations
 */
export async function generateVariations(request: VariationGenerationRequest) {
  try {
    const response = await apiClient.post('/generation/variations', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || 'Failed to generate variations');
  }
}

/**
 * Enhance a prompt using AI
 */
export async function enhancePrompt(
  prompt: string,
  style?: string,
  pose?: SpritePose,
  additionalContext?: string
) {
  try {
    const response = await apiClient.post('/generation/enhance-prompt', {
      prompt,
      style,
      pose,
      additionalContext
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || 'Failed to enhance prompt');
  }
}

/**
 * Get available poses
 */
export async function getAvailablePoses() {
  try {
    const response = await apiClient.get('/generation/poses');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || 'Failed to get poses');
  }
}

/**
 * Check generation service status
 */
export async function getGenerationStatus() {
  try {
    const response = await apiClient.get('/generation/status');
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || 'Failed to get status');
  }
}

/**
 * Upload and process image
 */
export async function uploadImage(file: File) {
  try {
    const formData = new FormData();
    formData.append('image', file);

    const response = await apiClient.post('/image/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || 'Failed to upload image');
  }
}

/**
 * Remove background from image
 */
export async function removeBackground(
  imageData: string,
  backgroundColor: string = '#FF00FF',
  tolerance: number = 10
) {
  try {
    const response = await apiClient.post('/image/remove-background', {
      imageData,
      backgroundColor,
      tolerance
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || 'Failed to remove background');
  }
}

/**
 * Resize image
 */
export async function resizeImage(
  imageData: string,
  width: number,
  height: number,
  maintainAspectRatio: boolean = true
) {
  try {
    const response = await apiClient.post('/image/resize', {
      imageData,
      width,
      height,
      maintainAspectRatio
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || 'Failed to resize image');
  }
}

/**
 * Generate spritesheet
 */
export async function generateSpritesheet(request: SpritesheetGenerationRequest) {
  try {
    const response = await apiClient.post('/image/spritesheet', request);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || 'Failed to generate spritesheet');
  }
}

/**
 * Apply pixelation effect
 */
export async function pixelateImage(imageData: string, pixelSize: number = 4) {
  try {
    const response = await apiClient.post('/image/pixelate', {
      imageData,
      pixelSize
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || 'Failed to pixelate image');
  }
}

/**
 * Get image information
 */
export async function getImageInfo(imageData: string) {
  try {
    const response = await apiClient.post('/image/info', { imageData });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || 'Failed to get image info');
  }
}

/**
 * Health check
 */
export async function healthCheck() {
  try {
    const response = await apiClient.get('/health');
    return response.data;
  } catch (error: any) {
    throw new Error('API is not available');
  }
}

export default apiClient;
