import { ColorPalette, SpritePose } from '../types';

/**
 * Generate an enhanced prompt for AI sprite generation
 */
export function generateSpritePrompt(
  baseDescription: string,
  pose: SpritePose,
  colorPalette?: ColorPalette,
  style: string = 'pixel art',
  additionalInstructions?: string
): string {
  let prompt = `Generate a ${style} sprite of ${baseDescription}`;

  // Add pose specification
  prompt += ` in ${pose} pose`;

  // Add style specifications
  prompt += `, 32x32 pixels, clean pixel art style, transparent background`;

  // Add color palette if provided
  if (colorPalette && colorPalette.colors.length > 0) {
    prompt += `, using color palette: ${colorPalette.colors.join(', ')}`;
  }

  // Add pose-specific instructions
  const poseInstructions = getPoseInstructions(pose);
  if (poseInstructions) {
    prompt += `, ${poseInstructions}`;
  }

  // Add general pixel art guidelines
  prompt += `, sharp edges, no anti-aliasing, retro game style, consistent with previous sprites`;

  // Add additional instructions if provided
  if (additionalInstructions) {
    prompt += `, ${additionalInstructions}`;
  }

  return prompt;
}

/**
 * Generate a character consistency prompt for maintaining appearance across poses
 */
export function generateConsistencyPrompt(
  baseDescription: string,
  newPose: SpritePose,
  existingSprites: string[] = []
): string {
  let prompt = `Generate a pixel art sprite that matches the character design from the reference images. `;
  prompt += `The character is: ${baseDescription}. `;
  prompt += `Show the character in ${newPose} pose. `;
  prompt += `Maintain exact same colors, proportions, and design elements as the reference sprites. `;
  prompt += `32x32 pixels, transparent background, clean pixel art style.`;

  return prompt;
}

/**
 * Get pose-specific instructions for better AI generation
 */
function getPoseInstructions(pose: SpritePose): string {
  const instructions: Record<SpritePose, string> = {
    idle: 'standing upright, relaxed posture, facing forward or slightly angled',
    walking: 'mid-step, one foot forward, arms swinging naturally',
    running: 'dynamic running pose, leaning forward, arms pumping',
    jumping: 'legs bent, arms raised, body in mid-air position',
    attacking: 'weapon or fist extended, aggressive stance, action pose',
    defending: 'defensive posture, shield up or arms protecting body',
    casting: 'magical pose, hands glowing or raised, mystical energy',
    hurt: 'recoiling, hand to wound, pained expression',
    dying: 'falling or collapsed pose, dramatic death animation',
    celebrating: 'arms raised in victory, happy expression, triumphant pose',
    crouching: 'low to ground, knees bent, sneaking or hiding pose',
    climbing: 'hands and feet positioned for climbing, vertical movement'
  };

  return instructions[pose] || '';
}

/**
 * Generate variation prompt for character modifications
 */
export function generateVariationPrompt(
  baseDescription: string,
  variation: string,
  pose: SpritePose = 'idle',
  colorPalette?: ColorPalette
): string {
  let prompt = `Generate a pixel art sprite of ${baseDescription} ${variation}`;
  prompt += ` in ${pose} pose`;
  prompt += `, 32x32 pixels, transparent background, clean pixel art style`;

  if (colorPalette && colorPalette.colors.length > 0) {
    prompt += `, using color palette: ${colorPalette.colors.join(', ')}`;
  }

  prompt += `, maintain character's core design while adding the variation`;

  return prompt;
}

/**
 * Generate color palette description for prompts
 */
export function generateColorPaletteDescription(palette: ColorPalette): string {
  if (palette.colors.length === 0) return '';

  const colorNames = palette.colors.map(color => {
    // Convert hex to color name approximation
    return hexToColorName(color);
  });

  return `limited color palette using: ${colorNames.join(', ')} (${palette.colors.join(', ')})`;
}

/**
 * Convert hex color to approximate color name
 */
function hexToColorName(hex: string): string {
  const colorMap: Record<string, string> = {
    '#FF0000': 'red',
    '#00FF00': 'green',
    '#0000FF': 'blue',
    '#FFFF00': 'yellow',
    '#FF00FF': 'magenta',
    '#00FFFF': 'cyan',
    '#FFFFFF': 'white',
    '#000000': 'black',
    '#808080': 'gray',
    '#800000': 'dark red',
    '#008000': 'dark green',
    '#000080': 'dark blue',
    '#FFA500': 'orange',
    '#800080': 'purple',
    '#A52A2A': 'brown',
    '#FFC0CB': 'pink'
  };

  // Return exact match if found
  if (colorMap[hex.toUpperCase()]) {
    return colorMap[hex.toUpperCase()];
  }

  // Return hex if no close match
  return hex;
}

/**
 * Generate style-specific instructions
 */
export function generateStyleInstructions(style: string): string {
  const styleInstructions: Record<string, string> = {
    'pixel art': 'clean pixel art, sharp edges, no anti-aliasing, retro game style',
    '8-bit': 'classic 8-bit video game style, limited colors, blocky design',
    '16-bit': '16-bit era graphics, more detailed than 8-bit, smooth animations',
    'chibi': 'cute chibi style, oversized head, small body, adorable features',
    'realistic': 'realistic proportions while maintaining pixel art aesthetic',
    'cartoon': 'cartoon-like features, exaggerated expressions, colorful'
  };

  return styleInstructions[style] || styleInstructions['pixel art'];
}

/**
 * Validate and clean prompt text
 */
export function cleanPrompt(prompt: string): string {
  // Remove excessive whitespace
  prompt = prompt.replace(/\s+/g, ' ').trim();
  
  // Remove duplicate phrases
  const words = prompt.split(' ');
  const uniqueWords = [...new Set(words)];
  
  // Rejoin if significant duplicates were removed
  if (uniqueWords.length < words.length * 0.8) {
    return uniqueWords.join(' ');
  }
  
  return prompt;
}
