#!/usr/bin/env node

/**
 * Comprehensive test script for Animagine XL 4.0 integration
 * Tests the complete integration from API endpoints to model generation
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3001/api/generation';

async function testModelAvailability() {
  console.log('🔍 Testing model availability...');
  
  try {
    const response = await axios.get(`${BASE_URL}/models/available`);
    
    if (response.data.success) {
      console.log('✅ Models endpoint working');
      console.log('📊 Available models:', Object.keys(response.data.models));
      
      // Check specific models
      const models = response.data.models;
      
      if (models.NANO_BANANA) {
        console.log(`✅ NANO BANANA: ${models.NANO_BANANA.status}`);
      }
      
      if (models.ANIMAGINE_XL) {
        console.log(`✅ Animagine XL: ${models.ANIMAGINE_XL.status}`);
      } else {
        console.log('⚠️ Animagine XL not available - may need setup');
      }
      
      return models;
    } else {
      console.log('❌ Models endpoint failed');
      return null;
    }
  } catch (error) {
    console.log('❌ Error testing model availability:', error.message);
    return null;
  }
}

async function testSingleSpriteGeneration(aiModel = 'NANO_BANANA') {
  console.log(`🎨 Testing single sprite generation with ${aiModel}...`);
  
  try {
    const response = await axios.post(`${BASE_URL}/sprite`, {
      prompt: 'knight warrior',
      pose: 'idle',
      style: 'pixel art',
      aiModel: aiModel,
      direction: 'south'
    }, {
      timeout: 120000 // 2 minute timeout
    });
    
    if (response.data.success) {
      console.log(`✅ ${aiModel} sprite generation successful`);
      console.log(`📊 Metadata:`, response.data.metadata);
      
      // Save test image if available
      if (response.data.imageData) {
        const imageData = response.data.imageData.split(',')[1];
        const buffer = Buffer.from(imageData, 'base64');
        const filename = `test_sprite_${aiModel.toLowerCase()}.png`;
        fs.writeFileSync(filename, buffer);
        console.log(`💾 Test sprite saved as ${filename}`);
      }
      
      return true;
    } else {
      console.log(`❌ ${aiModel} sprite generation failed:`, response.data.error);
      return false;
    }
  } catch (error) {
    console.log(`❌ Error testing ${aiModel} sprite generation:`, error.message);
    return false;
  }
}

async function testDirectionalGeneration() {
  console.log('🧭 Testing directional sprite generation...');
  
  try {
    const response = await axios.post(`${BASE_URL}/sprite/directional`, {
      prompt: 'mage wizard',
      directions: ['north', 'south', 'east', 'west'],
      action: 'idle',
      animationVersion: 'v2',
      aiModel: 'ANIMAGINE_XL'
    }, {
      timeout: 300000 // 5 minute timeout for multiple generations
    });
    
    if (response.data.success) {
      console.log('✅ Directional generation successful');
      console.log(`📊 Generated ${response.data.totalGenerated} directional sprites`);
      
      // Save directional sprites
      response.data.directionalSprites.forEach((sprite, index) => {
        if (sprite.imageData) {
          const imageData = sprite.imageData.split(',')[1];
          const buffer = Buffer.from(imageData, 'base64');
          const filename = `test_directional_${sprite.direction}.png`;
          fs.writeFileSync(filename, buffer);
          console.log(`💾 Directional sprite saved as ${filename}`);
        }
      });
      
      return true;
    } else {
      console.log('❌ Directional generation failed:', response.data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Error testing directional generation:', error.message);
    return false;
  }
}

async function testFallbackLogic() {
  console.log('🔄 Testing fallback logic...');
  
  // Test with a model that might not be available
  try {
    const response = await axios.post(`${BASE_URL}/sprite`, {
      prompt: 'test character',
      pose: 'idle',
      style: 'anime',
      aiModel: 'ANIMAGINE_XL' // This might fallback to NANO_BANANA if not available
    }, {
      timeout: 120000
    });
    
    if (response.data.success) {
      console.log('✅ Fallback logic working');
      console.log(`📊 Used model: ${response.data.metadata?.model}`);
      return true;
    } else {
      console.log('❌ Fallback logic failed:', response.data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Error testing fallback logic:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Animagine XL 4.0 Integration Tests');
  console.log('=' * 50);
  
  const results = {
    modelAvailability: false,
    nanoBananaGeneration: false,
    animagineGeneration: false,
    directionalGeneration: false,
    fallbackLogic: false
  };
  
  // Test 1: Model Availability
  const models = await testModelAvailability();
  results.modelAvailability = models !== null;
  
  // Test 2: NANO BANANA Generation
  results.nanoBananaGeneration = await testSingleSpriteGeneration('NANO_BANANA');
  
  // Test 3: Animagine XL Generation (if available)
  if (models && models.ANIMAGINE_XL && models.ANIMAGINE_XL.status === 'available') {
    results.animagineGeneration = await testSingleSpriteGeneration('ANIMAGINE_XL');
    
    // Test 4: Directional Generation (only if Animagine XL is available)
    if (results.animagineGeneration) {
      results.directionalGeneration = await testDirectionalGeneration();
    }
  } else {
    console.log('⚠️ Skipping Animagine XL tests - model not available');
  }
  
  // Test 5: Fallback Logic
  results.fallbackLogic = await testFallbackLogic();
  
  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('=' * 30);
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(Boolean).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Animagine XL 4.0 integration is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the output above for details.');
  }
}

// Check if server is running
async function checkServer() {
  try {
    await axios.get('http://localhost:3001/api/generation/status');
    return true;
  } catch (error) {
    console.log('❌ Server not running. Please start the server first:');
    console.log('   npm run dev  (or)  npx ts-node backend/server.ts');
    return false;
  }
}

// Main execution
async function main() {
  if (await checkServer()) {
    await runAllTests();
  }
}

main().catch(console.error);
