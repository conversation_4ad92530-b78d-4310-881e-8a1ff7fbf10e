#!/usr/bin/env python3
"""
Animagine XL 4.0 Model Download Script
Downloads the model from Hugging Face and sets up the local cache
"""

import os
import sys
import torch
from diffusers import StableDiffusionXLPipeline
from huggingface_hub import login, HfApi
import argparse

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking requirements...")
    
    # Check CUDA availability
    if not torch.cuda.is_available():
        print("⚠️ CUDA is not available. The model will run on CPU (very slow).")
        return False
    
    # Check VRAM
    if torch.cuda.is_available():
        vram_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        print(f"🎮 GPU VRAM: {vram_gb:.1f}GB")
        if vram_gb < 3.5:
            print("⚠️ Warning: Less than 8GB VRAM detected. Model may not run properly.")
            return False
    
    return True

def download_model(force_download=False):
    """Download Animagine XL 4.0 model"""
    model_id = "cagliostrolab/animagine-xl-4.0"
    cache_dir = "./ai-models/cache"
    
    print(f"📥 Downloading {model_id}...")
    print(f"💾 Cache directory: {cache_dir}")
    
    try:
        # Create cache directory
        os.makedirs(cache_dir, exist_ok=True)
        
        # Download the model
        pipeline = StableDiffusionXLPipeline.from_pretrained(
            model_id,
            torch_dtype=torch.float16,
            use_safetensors=True,
            cache_dir=cache_dir,
            force_download=force_download
        )
        
        print("✅ Model downloaded successfully!")
        
        # Test model loading
        print("🧪 Testing model loading...")
        pipeline.to("cuda" if torch.cuda.is_available() else "cpu")
        print("✅ Model loaded successfully!")
        
        # Clean up memory
        del pipeline
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        return True
        
    except Exception as e:
        print(f"❌ Error downloading model: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Download Animagine XL 4.0 model")
    parser.add_argument("--force", action="store_true", help="Force re-download even if model exists")
    parser.add_argument("--login", action="store_true", help="Login to Hugging Face first")
    args = parser.parse_args()
    
    print("🎨 Animagine XL 4.0 Model Download Script")
    print("=" * 50)
    
    # Login to Hugging Face if requested
    if args.login:
        print("🔑 Please login to Hugging Face...")
        try:
            login()
            print("✅ Logged in successfully!")
        except Exception as e:
            print(f"❌ Login failed: {e}")
            sys.exit(1)
    
    # Check requirements
    if not check_requirements():
        print("❌ Requirements not met. Please check your setup.")
        sys.exit(1)
    
    # Download model
    if download_model(force_download=args.force):
        print("\n🎉 Setup complete!")
        print("📝 Next steps:")
        print("   1. Activate the environment: source ai-models/animagine-xl-env/bin/activate")
        print("   2. Test the integration: python python-bridge/animagine_interface.py")
    else:
        print("\n❌ Setup failed. Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
