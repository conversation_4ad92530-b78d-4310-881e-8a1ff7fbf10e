import React, { useState } from 'react';
import { Character, ColorPalette } from '../../types';
import { UseSpriteReturn } from '../../hooks/useSprite';
import { UseImageProcessingReturn } from '../../hooks/useImageProcessing';
import CharacterForm from '../forms/CharacterForm';
import ColorPaletteEditor from '../editors/ColorPaletteEditor';
import ReferenceImageUpload from '../upload/ReferenceImageUpload';

interface ConceptionStageProps {
  spriteHook: UseSpriteReturn;
  imageProcessingHook: UseImageProcessingReturn;
  onNext: () => void;
}

const ConceptionStage: React.FC<ConceptionStageProps> = ({
  spriteHook,
  imageProcessingHook,
  onNext
}) => {
  const [characterName, setCharacterName] = useState('');
  const [characterDescription, setCharacterDescription] = useState('');
  const [selectedPalette, setSelectedPalette] = useState<ColorPalette | null>(null);
  const [referenceImages, setReferenceImages] = useState<string[]>([]);
  const [style, setStyle] = useState('pixel art');

  const { setCurrentCharacter } = spriteHook;
  const { uploadAndProcessImage } = imageProcessingHook;

  // Default color palettes
  const defaultPalettes: ColorPalette[] = [
    {
      id: 'retro',
      name: 'Retro Game',
      colors: ['#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'],
      isDefault: true
    },
    {
      id: 'earth',
      name: 'Earth Tones',
      colors: ['#8B4513', '#D2691E', '#CD853F', '#DEB887', '#F4A460', '#BC8F8F', '#696969', '#2F4F4F'],
      isDefault: true
    },
    {
      id: 'fantasy',
      name: 'Fantasy',
      colors: ['#4B0082', '#8A2BE2', '#9932CC', '#BA55D3', '#DA70D6', '#EE82EE', '#DDA0DD', '#E6E6FA'],
      isDefault: true
    },
    {
      id: 'monochrome',
      name: 'Monochrome',
      colors: ['#000000', '#333333', '#666666', '#999999', '#CCCCCC', '#FFFFFF'],
      isDefault: true
    }
  ];

  const handleReferenceImageUpload = async (files: File[]) => {
    const uploadedImages: string[] = [];
    
    for (const file of files) {
      try {
        const imageData = await uploadAndProcessImage(file);
        if (imageData) {
          uploadedImages.push(imageData);
        }
      } catch (error) {
        console.error('Failed to upload reference image:', error);
      }
    }
    
    setReferenceImages(prev => [...prev, ...uploadedImages]);
  };

  const handleRemoveReferenceImage = (index: number) => {
    setReferenceImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleCreateCharacter = () => {
    if (!characterName.trim() || !characterDescription.trim()) {
      return;
    }

    const character: Character = {
      id: `char_${Date.now()}`,
      name: characterName.trim(),
      description: characterDescription.trim(),
      sprites: [],
      colorPalette: selectedPalette || defaultPalettes[0],
      variations: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log('🎭 Character created:', character);
    setCurrentCharacter(character);
    console.log('🎭 Character set in hook, proceeding to next stage');
    onNext();
  };

  const canProceed = characterName.trim() && characterDescription.trim();

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-4">Character Conception</h2>
        <p className="text-gray-400 max-w-2xl mx-auto">
          Define your character's appearance, style, and personality. This information will guide the AI in creating consistent sprites across all poses.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Character Details */}
        <div className="space-y-6">
          {/* Character Form */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Character Details</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Character Name
                </label>
                <input
                  type="text"
                  value={characterName}
                  onChange={(e) => setCharacterName(e.target.value)}
                  placeholder="e.g., Warrior Knight, Pixel Mage, Robot Hero"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  value={characterDescription}
                  onChange={(e) => setCharacterDescription(e.target.value)}
                  placeholder="Describe your character's appearance, clothing, weapons, and distinctive features..."
                  rows={4}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Art Style
                </label>
                <select
                  value={style}
                  onChange={(e) => setStyle(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="pixel art">Pixel Art</option>
                  <option value="8-bit">8-bit Style</option>
                  <option value="16-bit">16-bit Style</option>
                  <option value="chibi">Chibi Style</option>
                  <option value="cartoon">Cartoon Style</option>
                </select>
              </div>
            </div>
          </div>

          {/* Color Palette */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Color Palette</h3>
            <ColorPaletteEditor
              palettes={defaultPalettes}
              selectedPalette={selectedPalette}
              onPaletteSelect={setSelectedPalette}
            />
          </div>
        </div>

        {/* Right Column - Reference Images */}
        <div className="space-y-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Reference Images (Optional)</h3>
            <p className="text-gray-400 text-sm mb-4">
              Upload reference images to help the AI understand your character's design. These will be used to maintain consistency across all generated sprites.
            </p>
            
            <ReferenceImageUpload
              onUpload={handleReferenceImageUpload}
              images={referenceImages}
              onRemove={handleRemoveReferenceImage}
              maxImages={5}
            />
          </div>

          {/* Tips */}
          <div className="bg-blue-900 bg-opacity-50 border border-blue-700 rounded-lg p-6">
            <h4 className="text-lg font-semibold text-blue-300 mb-3">💡 Tips for Better Results</h4>
            <ul className="text-blue-200 text-sm space-y-2">
              <li>• Be specific about clothing, armor, and accessories</li>
              <li>• Mention distinctive features like hair color, eye color, etc.</li>
              <li>• Include weapon or tool descriptions if relevant</li>
              <li>• Reference images help maintain consistency</li>
              <li>• Choose a color palette that matches your game's style</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center pt-8">
        <button
          onClick={handleCreateCharacter}
          disabled={!canProceed}
          className={`px-8 py-3 rounded-lg font-semibold transition-all ${
            canProceed
              ? 'bg-blue-600 hover:bg-blue-700 text-white'
              : 'bg-gray-600 text-gray-400 cursor-not-allowed'
          }`}
        >
          Create Character & Continue
        </button>
      </div>
    </div>
  );
};

export default ConceptionStage;
