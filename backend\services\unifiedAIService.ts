import GeminiService from './geminiService';
import AnimagineXLService from './animagineService';

interface AIModel {
  name: string;
  type: 'cloud' | 'local';
  capabilities: ModelCapabilities;
  rateLimits?: RateLimitConfig;
}

interface ModelCapabilities {
  maxResolution: { width: number; height: number };
  supportedFormats: string[];
  directionalSupport: boolean;
  pixelArtOptimized: boolean;
  animationFrames: number;
  animeStyleOptimized?: boolean;
  characterFocused?: boolean;
}

interface RateLimitConfig {
  maxRequestsPerMinute: number;
  maxRequestsPerDay: number;
  maxTokensPerMinute: number;
}

interface UnifiedGenerationOptions {
  prompt: string;
  pose?: string;
  style?: string;
  direction?: string;
  aiModel?: string;
  referenceImages?: string[];
  size?: { width: number; height: number };
  colorPalette?: any;
}

interface UnifiedGenerationResult {
  success: boolean;
  imageData?: string;
  animationFrames?: string[];
  error?: string;
  enhancedPrompt?: string;
  frameCount?: number;
  metadata?: {
    aiGenerated: boolean;
    model: string;
    timestamp: number;
    [key: string]: any;
  };
}

const AI_MODELS: Record<string, AIModel> = {
  NANO_BANANA: {
    name: 'NANO BANANA (Gemini 2.5 Flash Image)',
    type: 'cloud',
    capabilities: {
      maxResolution: { width: 1024, height: 1024 },
      supportedFormats: ['PNG', 'JPG', 'WebP'],
      directionalSupport: true,
      pixelArtOptimized: false,
      animationFrames: 8
    },
    rateLimits: {
      maxRequestsPerMinute: 10,
      maxRequestsPerDay: 500,
      maxTokensPerMinute: 250000
    }
  },
  ANIMAGINE_XL: {
    name: 'Animagine XL 4.0 (Local)',
    type: 'local',
    capabilities: {
      maxResolution: { width: 1024, height: 1024 },
      supportedFormats: ['PNG', 'JPG'],
      directionalSupport: true,
      pixelArtOptimized: true,
      animationFrames: 12,
      animeStyleOptimized: true,
      characterFocused: true
    }
  }
};

class UnifiedAIService {
  private geminiService: GeminiService;
  private animagineService: AnimagineXLService;

  constructor() {
    this.geminiService = new GeminiService();
    this.animagineService = new AnimagineXLService();
  }

  async generateSprite(options: UnifiedGenerationOptions): Promise<UnifiedGenerationResult> {
    const selectedModel = options.aiModel || this.selectOptimalModel(options);
    
    try {
      return await this.generateWithModel(selectedModel, options);
    } catch (error) {
      console.log(`Primary model ${selectedModel} failed, trying fallback...`);
      const fallbackModel = this.selectFallbackModel(selectedModel);
      return await this.generateWithModel(fallbackModel, options);
    }
  }

  private async generateWithModel(modelId: string, options: UnifiedGenerationOptions): Promise<UnifiedGenerationResult> {
    switch (modelId) {
      case 'NANO_BANANA':
        return await this.generateWithNanoBanana(options);
      
      case 'ANIMAGINE_XL':
        return await this.generateWithAnimagineXL(options);
      
      default:
        throw new Error(`Unknown model: ${modelId}`);
    }
  }

  private async generateWithNanoBanana(options: UnifiedGenerationOptions): Promise<UnifiedGenerationResult> {
    const geminiOptions = {
      prompt: options.prompt,
      pose: options.pose || 'idle',
      style: options.style || 'pixel art',
      referenceImages: options.referenceImages,
      size: options.size,
      colorPalette: options.colorPalette
    };

    const result = await this.geminiService.generateSprite(geminiOptions);
    
    return {
      ...result,
      metadata: {
        ...result.metadata,
        model: 'NANO_BANANA',
        aiGenerated: true,
        timestamp: Date.now()
      }
    };
  }

  private async generateWithAnimagineXL(options: UnifiedGenerationOptions): Promise<UnifiedGenerationResult> {
    const animagineOptions = {
      prompt: options.prompt,
      pose: options.pose || 'idle',
      style: options.style || 'anime',
      direction: options.direction,
      width: options.size?.width || 1024,
      height: options.size?.height || 1024,
      target_size: 512,
      num_inference_steps: 28,
      guidance_scale: 12.0
    };

    const result = await this.animagineService.generateSprite(animagineOptions);
    
    return {
      success: result.success,
      imageData: result.imageData,
      error: result.error,
      enhancedPrompt: result.metadata?.prompt,
      frameCount: 1,
      metadata: {
        aiGenerated: true,
        model: 'ANIMAGINE_XL',
        timestamp: Date.now(),
        ...result.metadata
      }
    };
  }

  selectOptimalModel(options: UnifiedGenerationOptions): string {
    // If anime style is requested, prefer Animagine XL
    if (options.style?.toLowerCase().includes('anime') || 
        options.style?.toLowerCase().includes('manga')) {
      return 'ANIMAGINE_XL';
    }
    
    // If pixel art is requested and local model is available
    if (options.style?.toLowerCase().includes('pixel')) {
      return 'ANIMAGINE_XL';
    }
    
    // Default to NANO BANANA for general use
    return 'NANO_BANANA';
  }

  private selectFallbackModel(primaryModel: string): string {
    return primaryModel === 'NANO_BANANA' ? 'ANIMAGINE_XL' : 'NANO_BANANA';
  }

  async getAvailableModels(): Promise<Record<string, any>> {
    const models: Record<string, any> = {};
    
    // Check NANO BANANA status
    models.NANO_BANANA = {
      ...AI_MODELS.NANO_BANANA,
      status: 'available', // Gemini service is always available
      rateLimits: await this.geminiService.getRateLimitStatus()
    };
    
    // Check Animagine XL status
    const animagineAvailable = await this.animagineService.isAvailable();
    models.ANIMAGINE_XL = {
      ...AI_MODELS.ANIMAGINE_XL,
      status: animagineAvailable ? 'available' : 'unavailable'
    };
    
    return models;
  }

  async getModelInfo(modelId: string): Promise<any> {
    switch (modelId) {
      case 'NANO_BANANA':
        return {
          ...AI_MODELS.NANO_BANANA,
          status: 'available',
          rateLimits: await this.geminiService.getRateLimitStatus()
        };
      
      case 'ANIMAGINE_XL':
        return await this.animagineService.getModelInfo();
      
      default:
        throw new Error(`Unknown model: ${modelId}`);
    }
  }

  async shutdown(): Promise<void> {
    await this.animagineService.shutdown();
  }
}

export default UnifiedAIService;
