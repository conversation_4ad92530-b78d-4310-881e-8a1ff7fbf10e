# SpriteGen 🎨

**AI-Powered Sprite Generation Application**

SpriteGen is a cutting-edge sprite generation tool that combines **Google Gemini 2.5 Flash AI** for intelligent character analysis with **NANO BANANA image generation** to create professional-quality pixel art sprites. The application features intelligent rate limiting, sequential processing, and Canvas fallback to ensure reliable sprite generation for game development.

## � Features

### 🤖 AI-Powered Generation
- **Google Gemini 2.5 Flash Analysis**: Intelligent character feature extraction and analysis
- **NANO BANANA Image Generation**: True AI-generated pixel art sprites using Gemini 2.5 Flash Image Preview
- **Character Recognition**: Automatically identifies body types, clothing, weapons, and accessories
- **Style Intelligence**: AI-driven color palette selection and thematic consistency

### 🎮 Game Development Ready
- **32x32 Pixel Art**: Professional game-ready sprite dimensions
- **8-Frame Animation**: Smooth animation sequences for character movement
- **Multiple Poses**: Support for idle, walking, attacking, casting, and custom poses
- **Batch Generation**: Create multiple sprites with different poses simultaneously

### ⚡ Advanced Rate Limiting
- **Free Tier Optimization**: Maximizes Google Gemini Free Tier usage (10 RPM, 500 RPD)
- **Sequential Processing**: Queue-based system prevents concurrent API calls
- **Intelligent Backoff**: API-guided retry delays with exponential backoff
- **Real-time Monitoring**: Live rate limit status and usage tracking

### 🛡️ Reliable Fallback
- **Canvas Generation**: High-quality programmatic sprite generation when AI quotas are exceeded
- **Seamless Switching**: Automatic fallback ensures continuous operation
- **Feature Consistency**: Canvas fallback maintains character features and poses

## � Prerequisites

Before setting up SpriteGen, ensure you have the following installed:

- **Node.js 18.0.0 or higher** (recommended: 20.x LTS)
- **npm** or **yarn** package manager
- **Google Gemini API Key** (free tier available)

### Getting a Google Gemini API Key

1. Visit [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account
3. Create a new API key
4. Copy the API key for use in the application

## 🚀 Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd spritegen
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Setup
Create a `.env` file in the root directory:
```bash
touch .env
```

Add your Google Gemini API key to the `.env` file:
```env
GEMINI_API_KEY=your_gemini_api_key_here
PORT=3001
NODE_ENV=development
```

### 4. Create Required Directories
```bash
mkdir -p public/uploads
```

### 5. Verify Installation
Check that all dependencies are installed correctly:
```bash
npm list --depth=0
```

## �‍♂️ Running the Application

### Backend Server
Start the backend API server:
```bash
npx ts-node backend/server.ts
```

The backend server will start on **port 3001** by default.

### Frontend Development Server (Optional)
If you have a frontend implementation:
```bash
npm run dev
```

### Verify the Application is Running
1. **Backend Health Check**: Visit [http://localhost:3001/api/generation/status](http://localhost:3001/api/generation/status)
2. **Rate Limit Status**: Visit [http://localhost:3001/api/generation/rate-limits](http://localhost:3001/api/generation/rate-limits)

You should see JSON responses indicating the service is operational.

## 🏗️ Architecture

### Backend-Focused Architecture
- **Backend**: Node.js/Express.js with TypeScript
- **AI Integration**: Google Gemini 2.5 Flash + NANO BANANA Image Generation
- **Rate Limiting**: Intelligent queue-based sequential processing
- **Fallback System**: Canvas-based sprite generation

### Project Structure
```
spritegen/
├── backend/
│   ├── routes/
│   │   └── generation.ts    # API routes for sprite generation
│   ├── services/
│   │   └── geminiService.ts # Google Gemini AI integration
│   └── server.ts           # Express server setup
├── public/
│   └── uploads/            # File upload directory
├── .env                    # Environment variables
├── package.json           # Dependencies and scripts
└── README.md              # This file
```

## 🔧 API Endpoints

### Core Generation Endpoints

#### `POST /api/generation/sprite`
Generate a single sprite with AI analysis and image generation.

**Request Body:**
```json
{
  "prompt": "ice wizard with crystal staff",
  "pose": "casting",
  "style": "pixel art",
  "colorPalette": {
    "colors": ["#ADD8E6", "#4682B4", "#F0F8FF"]
  }
}
```

**Response:**
```json
{
  "success": true,
  "imageData": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "animationFrames": ["frame1_base64", "frame2_base64", "..."],
  "frameCount": 8,
  "metadata": {
    "prompt": "Enhanced prompt with AI analysis",
    "pose": "casting",
    "style": "pixel art",
    "aiGenerated": true,
    "timestamp": "2025-09-10T20:00:00.000Z"
  }
}
```

#### `POST /api/generation/sprites/batch`
Generate multiple sprites for different poses sequentially.

**Request Body:**
```json
{
  "prompt": "armored knight with sword",
  "poses": ["idle", "attacking", "defending"],
  "style": "pixel art"
}
```

#### `GET /api/generation/status`
Get service health status and rate limit information.

**Response:**
```json
{
  "success": true,
  "connected": true,
  "aiGenerationAvailable": true,
  "rateLimits": {
    "requests": {
      "perMinute": { "current": 2, "limit": 10, "resetIn": 45 },
      "perDay": { "current": 15, "limit": 500, "resetIn": 18 }
    }
  }
}
```

#### `GET /api/generation/rate-limits`
Get detailed rate limiting status and usage information.

## ⚡ Rate Limiting

SpriteGen implements intelligent rate limiting to maximize Google Gemini Free Tier usage while ensuring reliable operation.

### Free Tier Limits
- **Requests Per Minute (RPM)**: 10 requests
- **Requests Per Day (RPD)**: 500 requests
- **Tokens Per Minute (TPM)**: 250,000 tokens

### Sequential Processing
- **Queue-Based System**: All AI generation requests are processed sequentially
- **6-Second Spacing**: Optimal spacing between requests (10 requests/60 seconds)
- **No Concurrent Calls**: Prevents rate limit conflicts from parallel requests

### Intelligent Backoff
- **API-Guided Delays**: Uses retry delays specified by Google's API (e.g., "42s")
- **Exponential Backoff**: When no delay specified, uses 6s → 12s → 24s progression
- **Max Retries**: 3 attempts with intelligent spacing

### Monitoring
- **Real-time Status**: Live tracking of current usage vs. limits
- **Reset Timers**: Shows time until rate limit counters reset
- **Queue Status**: Visibility into pending requests and processing state

## 🛠️ Technology Stack

### Backend
- **Node.js 18+**: JavaScript runtime environment
- **TypeScript**: Type-safe JavaScript development
- **Express.js**: Web application framework
- **dotenv**: Environment variable management

### AI Integration
- **Google Gemini 2.5 Flash**: Text analysis and character feature extraction
- **NANO BANANA (Gemini 2.5 Flash Image)**: AI image generation for pixel art sprites
- **@google/generative-ai**: Official Google Generative AI SDK

### Image Processing
- **Canvas API**: Programmatic sprite generation (fallback)
- **node-canvas**: Server-side Canvas implementation
- **Base64 Encoding**: Image data transmission

### Development Tools
- **ts-node**: TypeScript execution for Node.js
- **cross-fetch**: Fetch API polyfill for Node.js compatibility

## � Troubleshooting

### Common Issues

#### 1. API Key Setup
**Problem**: `GEMINI_API_KEY environment variable is required`
**Solution**:
- Ensure `.env` file exists in the root directory
- Verify the API key is correctly set: `GEMINI_API_KEY=your_actual_api_key`
- Restart the server after adding the API key

#### 2. Node.js Version Compatibility
**Problem**: `fetch is not defined` or module compatibility errors
**Solution**:
- Upgrade to Node.js 18.0.0 or higher
- Use `nvm` to manage Node.js versions:
  ```bash
  nvm install 20
  nvm use 20
  ```

#### 3. Rate Limit Quota Exceeded
**Problem**: `429 Too Many Requests` errors
**Solution**:
- Check rate limit status: `GET /api/generation/rate-limits`
- Wait for rate limits to reset (shown in `resetIn` field)
- The system automatically falls back to Canvas generation
- Consider upgrading to a paid Google Cloud tier for higher limits

#### 4. TypeScript Compilation Errors
**Problem**: `Unable to compile TypeScript` errors
**Solution**:
- Ensure all dependencies are installed: `npm install`
- Check TypeScript configuration in `tsconfig.json`
- Verify file paths and imports are correct

#### 5. Upload Directory Permissions
**Problem**: File upload failures
**Solution**:
- Ensure `public/uploads` directory exists: `mkdir -p public/uploads`
- Check directory permissions: `chmod 755 public/uploads`

### Debug Mode
Enable detailed logging by setting:
```env
NODE_ENV=development
```

### Getting Help
1. Check the server logs for detailed error messages
2. Verify API connectivity: `GET /api/generation/status`
3. Test with simple requests before complex batch operations
4. Monitor rate limits to avoid quota issues

## 🚀 Deployment

### Production Setup
1. **Server Requirements**:
   - Node.js 18+ runtime environment
   - Sufficient disk space for uploads and generated sprites
   - Stable internet connection for Google Gemini API access

2. **Environment Configuration**:
   ```env
   GEMINI_API_KEY=your_production_api_key
   PORT=3001
   NODE_ENV=production
   ```

3. **Start Production Server**:
   ```bash
   npx ts-node backend/server.ts
   ```

4. **Process Management** (recommended):
   ```bash
   # Using PM2
   npm install -g pm2
   pm2 start "npx ts-node backend/server.ts" --name spritegen
   pm2 startup
   pm2 save
   ```

### Security Considerations
- Keep your Google Gemini API key secure and never commit it to version control
- Set up proper firewall rules for production deployment
- Consider implementing authentication for production use
- Monitor API usage to avoid unexpected charges

## 🧪 Testing

### Manual Testing
Test the API endpoints using curl or a tool like Postman:

```bash
# Health check
curl http://localhost:3001/api/generation/status

# Rate limit status
curl http://localhost:3001/api/generation/rate-limits

# Generate a sprite
curl -X POST http://localhost:3001/api/generation/sprite \
  -H "Content-Type: application/json" \
  -d '{"prompt":"test wizard","pose":"idle","style":"pixel art"}'
```

### Expected Behavior
- ✅ AI generation attempts with intelligent rate limiting
- ✅ Automatic fallback to Canvas generation when quotas exceeded
- ✅ Sequential processing of batch requests
- ✅ Proper error handling and logging

## 🤝 Contributing

We welcome contributions to SpriteGen! Here's how to get started:

1. **Fork the repository** on GitHub
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and test thoroughly
4. **Commit your changes**: `git commit -m 'Add amazing feature'`
5. **Push to the branch**: `git push origin feature/amazing-feature`
6. **Open a Pull Request** with a clear description of your changes

### Development Guidelines
- Follow TypeScript best practices
- Add appropriate error handling
- Update documentation for new features
- Test with both AI generation and Canvas fallback

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Google Gemini AI** for powerful text analysis and image generation capabilities
- **NANO BANANA** (Gemini 2.5 Flash Image Preview) for AI-powered pixel art generation
- **Node.js Community** for excellent runtime and package ecosystem
- **TypeScript Team** for type-safe JavaScript development
- **Express.js** for the robust web application framework

## 📞 Support

For support and questions:

1. **Check the Troubleshooting section** above for common issues
2. **Review server logs** for detailed error information
3. **Test API endpoints** to verify connectivity and functionality
4. **Monitor rate limits** to ensure optimal usage
5. **Open an issue** on GitHub for bugs or feature requests

---

**SpriteGen** 🎨 - Bringing your game characters to life with AI-powered sprite generation! ✨

*Powered by Google Gemini 2.5 Flash + NANO BANANA Image Generation*
