import express from 'express';
import GeminiService from '../services/geminiService';
import UnifiedAIService from '../services/unifiedAIService';

const router = express.Router();
let geminiService: GeminiService | null = null;
let unifiedAIService: UnifiedAIService | null = null;

// Initialize Gemini service lazily
function getGeminiService(): GeminiService {
  if (!geminiService) {
    geminiService = new GeminiService();
  }
  return geminiService;
}

// Initialize Unified AI service lazily
function getUnifiedAIService(): UnifiedAIService {
  if (!unifiedAIService) {
    unifiedAIService = new UnifiedAIService();
  }
  return unifiedAIService;
}

/**
 * Generate a single sprite using unified AI service
 */
router.post('/sprite', async (req, res) => {
  try {
    const { prompt, pose, referenceImages, colorPalette, style, size, aiModel, direction } = req.body;

    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Enhance prompt with pose and style information
    let enhancedPrompt = prompt;
    if (pose) {
      enhancedPrompt += ` in ${pose} pose`;
    }

    // Add color palette information
    if (colorPalette && colorPalette.colors) {
      enhancedPrompt += `, using colors: ${colorPalette.colors.join(', ')}`;
    }

    const result = await getUnifiedAIService().generateSprite({
      prompt: enhancedPrompt,
      pose: pose,
      style: style || 'pixel art',
      direction: direction,
      aiModel: aiModel,
      referenceImages: referenceImages || [],
      size: size || { width: 32, height: 32 },
      colorPalette: colorPalette
    });

    if (result.success) {
      res.json({
        success: true,
        imageData: result.imageData,
        animationFrames: result.animationFrames, // Include animation frames
        frameCount: result.frameCount,
        metadata: {
          prompt: enhancedPrompt,
          pose,
          style,
          size,
          timestamp: new Date().toISOString(),
          aiGenerated: result.frameCount && result.frameCount > 1
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    console.error('Sprite generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate sprite'
    });
  }
});

/**
 * Generate multiple sprites for different poses
 */
router.post('/sprites/batch', async (req, res) => {
  try {
    const { basePrompt, poses, referenceImages, colorPalette, style } = req.body;

    if (!basePrompt || !poses || !Array.isArray(poses)) {
      return res.status(400).json({ 
        error: 'Base prompt and poses array are required' 
      });
    }

    // Enhance base prompt with color palette
    let enhancedBasePrompt = basePrompt;
    if (colorPalette && colorPalette.colors) {
      enhancedBasePrompt += `, using colors: ${colorPalette.colors.join(', ')}`;
    }

    const results = await getGeminiService().generateMultipleSprites(
      enhancedBasePrompt,
      poses,
      referenceImages,
      colorPalette
    );

    const successfulResults = results.filter(r => r.result.success);
    const failedResults = results.filter(r => !r.result.success);

    res.json({
      success: true,
      results: results.map(r => ({
        pose: r.pose,
        success: r.result.success,
        imageData: r.result.imageData,
        error: r.result.error
      })),
      summary: {
        total: results.length,
        successful: successfulResults.length,
        failed: failedResults.length
      },
      metadata: {
        basePrompt: enhancedBasePrompt,
        poses,
        style: style || 'pixel art',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Batch sprite generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate sprites'
    });
  }
});

/**
 * Generate character variations
 */
router.post('/variations', async (req, res) => {
  try {
    const { basePrompt, variations, pose, referenceImages, colorPalette } = req.body;

    if (!basePrompt || !variations || !Array.isArray(variations)) {
      return res.status(400).json({ 
        error: 'Base prompt and variations array are required' 
      });
    }

    const results = [];

    for (const variation of variations) {
      const variationPrompt = `${basePrompt} ${variation}`;
      let enhancedPrompt = variationPrompt;
      
      if (pose) {
        enhancedPrompt += ` in ${pose} pose`;
      }

      if (colorPalette && colorPalette.colors) {
        enhancedPrompt += `, using colors: ${colorPalette.colors.join(', ')}`;
      }

      const result = await getGeminiService().generateSprite({
        prompt: enhancedPrompt,
        referenceImages: referenceImages || [],
        style: 'pixel art',
        pose: pose,
        colorPalette: colorPalette
      });

      results.push({
        variation,
        success: result.success,
        imageData: result.imageData,
        error: result.error
      });

      // Add delay between requests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    res.json({
      success: true,
      results,
      metadata: {
        basePrompt,
        variations,
        pose,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Variation generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate variations'
    });
  }
});

/**
 * Enhance a prompt using Gemini
 */
router.post('/enhance-prompt', async (req, res) => {
  try {
    const { prompt, style, pose, additionalContext } = req.body;

    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Create enhancement request
    let enhancementPrompt = `Enhance this sprite generation prompt for better AI image generation: "${prompt}"`;
    
    if (style) {
      enhancementPrompt += ` Style: ${style}.`;
    }
    
    if (pose) {
      enhancementPrompt += ` Pose: ${pose}.`;
    }
    
    if (additionalContext) {
      enhancementPrompt += ` Additional context: ${additionalContext}.`;
    }

    enhancementPrompt += ` Provide a detailed, specific prompt that would generate a high-quality pixel art sprite.`;

    const result = await getGeminiService().generateSprite({
      prompt: enhancementPrompt,
      style: 'text'
    });

    res.json({
      success: true,
      originalPrompt: prompt,
      enhancedPrompt: result.error || 'Enhancement not available', // Since we're getting error for now
      metadata: {
        style,
        pose,
        additionalContext,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Prompt enhancement error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to enhance prompt'
    });
  }
});

/**
 * Get available poses
 */
router.get('/poses', (req, res) => {
  const poses = [
    'idle',
    'walking',
    'running',
    'jumping',
    'attacking',
    'defending',
    'casting',
    'hurt',
    'dying',
    'celebrating',
    'crouching',
    'climbing'
  ];

  res.json({
    success: true,
    poses: poses.map(pose => ({
      id: pose,
      name: pose.charAt(0).toUpperCase() + pose.slice(1),
      description: getPoseDescription(pose)
    }))
  });
});

/**
 * Validate Gemini connection and get rate limit status
 */
router.get('/status', async (req, res) => {
  try {
    const geminiService = getGeminiService();
    const isConnected = await geminiService.validateConnection();
    const modelInfo = geminiService.getModelInfo();
    const rateLimitStatus = geminiService.getRateLimitStatus();

    res.json({
      success: true,
      connected: isConnected,
      aiGenerationAvailable: geminiService.isAIGenerationAvailable(),
      model: modelInfo,
      rateLimits: rateLimitStatus,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      connected: false,
      aiGenerationAvailable: false,
      error: 'Failed to validate connection'
    });
  }
});

/**
 * Get detailed rate limit status
 */
router.get('/rate-limits', async (req, res) => {
  try {
    const geminiService = getGeminiService();
    const rateLimitStatus = geminiService.getRateLimitStatus();

    res.json({
      success: true,
      rateLimits: rateLimitStatus,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Rate limit status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get rate limit status',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get pose description
 */
function getPoseDescription(pose: string): string {
  const descriptions: Record<string, string> = {
    idle: 'Standing upright, relaxed posture',
    walking: 'Mid-step, one foot forward',
    running: 'Dynamic running pose, leaning forward',
    jumping: 'Legs bent, body in mid-air position',
    attacking: 'Weapon or fist extended, aggressive stance',
    defending: 'Defensive posture, shield up or arms protecting',
    casting: 'Magical pose, hands glowing or raised',
    hurt: 'Recoiling, hand to wound, pained expression',
    dying: 'Falling or collapsed pose',
    celebrating: 'Arms raised in victory, happy expression',
    crouching: 'Low to ground, knees bent, sneaking pose',
    climbing: 'Hands and feet positioned for climbing'
  };

  return descriptions[pose] || 'Custom pose';
}

/**
 * Get available AI models
 */
router.get('/models/available', async (req, res) => {
  try {
    const models = await getUnifiedAIService().getAvailableModels();
    res.json({
      success: true,
      models: models
    });
  } catch (error) {
    console.error('Error getting available models:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get available models'
    });
  }
});

/**
 * Get specific model information
 */
router.get('/models/:modelId', async (req, res) => {
  try {
    const { modelId } = req.params;
    const modelInfo = await getUnifiedAIService().getModelInfo(modelId);
    res.json({
      success: true,
      model: modelInfo
    });
  } catch (error) {
    console.error('Error getting model info:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get model information'
    });
  }
});

/**
 * Generate directional sprites
 */
router.post('/sprite/directional', async (req, res) => {
  try {
    const { prompt, directions, action, animationVersion, aiModel, pixelArtOptions } = req.body;

    if (!prompt || !directions || !Array.isArray(directions)) {
      return res.status(400).json({
        error: 'Prompt and directions array are required'
      });
    }

    const results: any[] = [];

    // Generate sprites for each direction
    for (const direction of directions) {
      const result = await getUnifiedAIService().generateSprite({
        prompt: prompt,
        pose: action || 'idle',
        style: pixelArtOptions?.style || 'pixel art',
        direction: direction,
        aiModel: aiModel || 'ANIMAGINE_XL'
      });

      if (result.success) {
        results.push({
          direction: direction,
          imageData: result.imageData,
          metadata: result.metadata
        });
      }
    }

    res.json({
      success: true,
      directionalSprites: results,
      totalGenerated: results.length,
      metadata: {
        prompt: prompt,
        directions: directions,
        action: action,
        animationVersion: animationVersion,
        aiModel: aiModel,
        timestamp: Date.now()
      }
    });

  } catch (error) {
    console.error('Error generating directional sprites:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate directional sprites'
    });
  }
});

export default router;
